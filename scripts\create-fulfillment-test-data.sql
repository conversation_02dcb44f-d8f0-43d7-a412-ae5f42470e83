-- Create realistic test data for order fulfillment analytics
-- This script creates 6 orders with complete timestamp data for testing

DO $$
DECLARE
  customer_id INTEGER;
  driver_id INTEGER;
  business_id INTEGER;
  order_id INTEGER;
  base_time TIMESTAMP;
BEGIN
  -- Get test customer
  SELECT id INTO customer_id FROM users WHERE role = 'customer' LIMIT 1;
  IF customer_id IS NULL THEN
    RAISE EXCEPTION 'No customer found. Please create test users first.';
  END IF;

  -- Get test driver
  SELECT id INTO driver_id FROM users WHERE role = 'driver' LIMIT 1;
  IF driver_id IS NULL THEN
    RAISE EXCEPTION 'No driver found. Please create test users first.';
  END IF;

  -- Get test business
  SELECT id INTO business_id FROM businesses LIMIT 1;
  IF business_id IS NULL THEN
    RAISE EXCEPTION 'No business found. Please create test businesses first.';
  END IF;

  -- Base time for calculations (2 hours ago)
  base_time := NOW() - INTERVAL '2 hours';

  -- Delete existing test orders
  DELETE FROM orders WHERE order_number LIKE 'ORD-%';

  -- ORDER 1: ORD-001-EARLY (Everything faster than expected)
  INSERT INTO orders (
    order_number, user_id, business_id, driver_id,
    business_name, customer_name, delivery_parish,
    status, total_amount, delivery_fee, service_fee,
    delivery_address, payment_method, payment_status,
    delivery_type, preparation_time, estimated_delivery_time,
    driver_response_time_seconds, pickup_delay_minutes,
    created_at, ready_time, offered_at, assigned_at,
    picked_up_at, out_for_delivery_at, delivered_at
  ) VALUES (
    'ORD-001-EARLY', customer_id, business_id, driver_id,
    'St. Brelade''s Bistro', 'Alice Johnson', 'St. Brelade',
    'delivered', 28.50, 3.50, 1.25,
    '45 La Route de la Baie, St. Brelade, JE3 8EF', 'card', 'completed',
    'delivery', 25, 40,
    120, 2, -- 2min driver response, 2min pickup delay
    base_time,                           -- Order created
    base_time + INTERVAL '20 minutes',   -- Ready (20min actual vs 25min estimated)
    base_time + INTERVAL '21 minutes',   -- Offered to driver
    base_time + INTERVAL '23 minutes',   -- Driver accepted (2min response)
    base_time + INTERVAL '25 minutes',   -- Driver picked up (2min delay)
    base_time + INTERVAL '26 minutes',   -- Out for delivery
    base_time + INTERVAL '37 minutes'    -- Delivered (12min delivery vs 15min estimated)
  );

  -- ORDER 2: ORD-002-ONTIME (Exactly as estimated)
  INSERT INTO orders (
    order_number, user_id, business_id, driver_id,
    business_name, customer_name, delivery_parish,
    status, total_amount, delivery_fee, service_fee,
    delivery_address, payment_method, payment_status,
    delivery_type, preparation_time, estimated_delivery_time,
    driver_response_time_seconds, pickup_delay_minutes,
    created_at, ready_time, offered_at, assigned_at,
    picked_up_at, out_for_delivery_at, delivered_at
  ) VALUES (
    'ORD-002-ONTIME', customer_id, business_id, driver_id,
    '2 Tasty', 'Bob Smith', 'St. Helier',
    'delivered', 22.75, 2.50, 1.00,
    '12 Hill Street, St. Helier, JE2 4UA', 'card', 'completed',
    'delivery', 20, 35,
    300, 5, -- 5min driver response, 5min pickup delay
    base_time + INTERVAL '10 minutes',   -- Order created
    base_time + INTERVAL '30 minutes',   -- Ready (20min exactly as estimated)
    base_time + INTERVAL '31 minutes',   -- Offered to driver
    base_time + INTERVAL '36 minutes',   -- Driver accepted (5min response)
    base_time + INTERVAL '41 minutes',   -- Driver picked up (5min delay)
    base_time + INTERVAL '42 minutes',   -- Out for delivery
    base_time + INTERVAL '53 minutes'    -- Delivered (11min delivery, close to 15min estimated)
  );

  -- ORDER 3: ORD-003-DELAYED (Slower than expected)
  INSERT INTO orders (
    order_number, user_id, business_id, driver_id,
    business_name, customer_name, delivery_parish,
    status, total_amount, delivery_fee, service_fee,
    delivery_address, payment_method, payment_status,
    delivery_type, preparation_time, estimated_delivery_time,
    driver_response_time_seconds, pickup_delay_minutes,
    created_at, ready_time, offered_at, assigned_at,
    picked_up_at, out_for_delivery_at, delivered_at
  ) VALUES (
    'ORD-003-DELAYED', customer_id, business_id, driver_id,
    'Any Business', 'Carol Wilson', 'St. Saviour',
    'delivered', 35.25, 4.00, 1.50,
    '78 La Grande Route de St. Saviour, St. Saviour, JE2 7LA', 'card', 'completed',
    'delivery', 30, 50,
    600, 10, -- 10min driver response, 10min pickup delay
    base_time + INTERVAL '20 minutes',   -- Order created
    base_time + INTERVAL '55 minutes',   -- Ready (35min actual vs 30min estimated)
    base_time + INTERVAL '56 minutes',   -- Offered to driver
    base_time + INTERVAL '66 minutes',   -- Driver accepted (10min response)
    base_time + INTERVAL '76 minutes',   -- Driver picked up (10min delay)
    base_time + INTERVAL '77 minutes',   -- Out for delivery
    base_time + INTERVAL '110 minutes'   -- Delivered (33min delivery with weather delays)
  );

  -- ORDER 4: ORD-004-MIXED1 (Fast prep, slow delivery)
  INSERT INTO orders (
    order_number, user_id, business_id, driver_id,
    business_name, customer_name, delivery_parish,
    status, total_amount, delivery_fee, service_fee,
    delivery_address, payment_method, payment_status,
    delivery_type, preparation_time, estimated_delivery_time,
    driver_response_time_seconds, pickup_delay_minutes,
    created_at, ready_time, offered_at, assigned_at,
    picked_up_at, out_for_delivery_at, delivered_at
  ) VALUES (
    'ORD-004-MIXED1', customer_id, business_id, driver_id,
    'Jersey Home Goods', 'David Brown', 'St. John',
    'delivered', 45.80, 5.50, 2.00,
    '23 La Rue de la Croix, St. John, JE3 4FL', 'card', 'completed',
    'delivery', 18, 33,
    180, 8, -- 3min driver response, 8min pickup delay
    base_time + INTERVAL '30 minutes',   -- Order created
    base_time + INTERVAL '42 minutes',   -- Ready (12min actual vs 18min estimated - fast!)
    base_time + INTERVAL '43 minutes',   -- Offered to driver
    base_time + INTERVAL '46 minutes',   -- Driver accepted (3min response)
    base_time + INTERVAL '54 minutes',   -- Driver picked up (8min delay)
    base_time + INTERVAL '55 minutes',   -- Out for delivery
    base_time + INTERVAL '84 minutes'    -- Delivered (29min delivery - traffic delays)
  );

  -- ORDER 5: ORD-005-SUPERFAST (Everything very fast)
  INSERT INTO orders (
    order_number, user_id, business_id, driver_id,
    business_name, customer_name, delivery_parish,
    status, total_amount, delivery_fee, service_fee,
    delivery_address, payment_method, payment_status,
    delivery_type, preparation_time, estimated_delivery_time,
    driver_response_time_seconds, pickup_delay_minutes,
    created_at, ready_time, offered_at, assigned_at,
    picked_up_at, out_for_delivery_at, delivered_at
  ) VALUES (
    'ORD-005-SUPERFAST', customer_id, business_id, driver_id,
    'A Business', 'Emma Davis', 'St. Helier',
    'delivered', 18.25, 2.00, 0.75,
    '5 Royal Square, St. Helier, JE2 4WA', 'card', 'completed',
    'delivery', 15, 25,
    30, 0, -- 30sec driver response, 0min pickup delay
    base_time + INTERVAL '40 minutes',   -- Order created
    base_time + INTERVAL '50 minutes',   -- Ready (10min actual vs 15min estimated)
    base_time + INTERVAL '51 minutes',   -- Offered to driver
    base_time + INTERVAL '51.5 minutes', -- Driver accepted (30sec response!)
    base_time + INTERVAL '51.5 minutes', -- Driver picked up immediately
    base_time + INTERVAL '52 minutes',   -- Out for delivery
    base_time + INTERVAL '59.5 minutes'  -- Delivered (7.5min delivery - super fast)
  );

  -- ORDER 6: ORD-006-MAJORDELAY (Multiple cascading issues)
  INSERT INTO orders (
    order_number, user_id, business_id, driver_id,
    business_name, customer_name, delivery_parish,
    status, total_amount, delivery_fee, service_fee,
    delivery_address, payment_method, payment_status,
    delivery_type, preparation_time, estimated_delivery_time,
    driver_response_time_seconds, pickup_delay_minutes,
    created_at, ready_time, offered_at, assigned_at,
    picked_up_at, out_for_delivery_at, delivered_at
  ) VALUES (
    'ORD-006-MAJORDELAY', customer_id, business_id, driver_id,
    'St. Brelade''s Bistro', 'Frank Miller', 'St. Ouen',
    'delivered', 52.75, 6.00, 2.25,
    '89 La Route de L''Etacq, St. Ouen, JE3 2HF', 'card', 'completed',
    'delivery', 35, 55,
    900, 20, -- 15min driver response, 20min pickup delay
    base_time + INTERVAL '50 minutes',   -- Order created
    base_time + INTERVAL '100 minutes',  -- Ready (50min actual vs 35min estimated - kitchen overwhelmed)
    base_time + INTERVAL '101 minutes',  -- Offered to driver
    base_time + INTERVAL '116 minutes',  -- Driver accepted (15min response - multiple offers)
    base_time + INTERVAL '136 minutes',  -- Driver picked up (20min delay - parking issues)
    base_time + INTERVAL '137 minutes',  -- Out for delivery
    base_time + INTERVAL '189 minutes'   -- Delivered (52min delivery - traffic + weather + navigation issues)
  );

  RAISE NOTICE 'Successfully created 6 test orders with complete fulfillment timestamps';
END $$;
