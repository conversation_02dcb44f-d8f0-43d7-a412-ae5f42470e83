"use client"

import { Clock, Truck, Store, AlertCircle } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"
import { useDeliveryMode } from "@/context/delivery-mode-context"
import { useEffect, useState } from "react"

interface BusinessStatusOverlayProps {
  businessId: string
  businessName?: string
  isTemporarilyClosed?: boolean
  closureMessage?: string
  deliveryAvailable?: boolean
  pickupAvailable?: boolean
  openingHours?: any
  userPostcode?: string
  className?: string
  variant?: "card" | "page"
}

interface BusinessStatus {
  isAvailable: boolean
  status: "open" | "closed" | "pickup_only" | "delivery_unavailable" | "no_service"
  message: string
  showOverlay: boolean
}

export default function BusinessStatusOverlay({
  businessId,
  businessName,
  isTemporarilyClosed,
  closureMessage,
  deliveryAvailable,
  pickupAvailable,
  openingHours,
  userPostcode,
  className,
  variant = "card"
}: BusinessStatusOverlayProps) {
  const { mode } = useDeliveryMode()
  const [isDeliveryAvailableToPostcode, setIsDeliveryAvailableToPostcode] = useState<boolean | null>(null)

  // Check if delivery is available to the user's postcode
  useEffect(() => {
    if (!userPostcode || !deliveryAvailable || !businessId) {
      setIsDeliveryAvailableToPostcode(null)
      return
    }

    const checkDeliveryAvailability = async () => {
      try {
        // Call API to check if delivery is available to this postcode for this business
        const response = await fetch(`/api/delivery/check-availability?businessId=${businessId}&postcode=${encodeURIComponent(userPostcode)}`)
        if (response.ok) {
          const data = await response.json()
          setIsDeliveryAvailableToPostcode(data.available)
        } else {
          // If API fails, assume delivery is available (graceful degradation)
          setIsDeliveryAvailableToPostcode(true)
        }
      } catch (error) {
        console.error('Error checking delivery availability:', error)
        // If API fails, assume delivery is available (graceful degradation)
        setIsDeliveryAvailableToPostcode(true)
      }
    }

    checkDeliveryAvailability()
  }, [businessId, userPostcode, deliveryAvailable])

  // Determine business status based on current conditions
  const getBusinessStatus = (): BusinessStatus => {
    // Check if business is temporarily closed
    if (isTemporarilyClosed) {
      return {
        isAvailable: false,
        status: "closed",
        message: closureMessage || "Temporarily closed",
        showOverlay: true
      }
    }

    // Check opening hours
    const isWithinOpeningHours = (() => {
      if (!openingHours) return true // If no opening hours defined, assume always open

      try {
        const now = new Date()
        const currentDay = now.getDay() // 0 = Sunday, 1 = Monday, etc.
        const currentTime = now.toTimeString().substring(0, 5) // 'HH:MM' format

        // Handle different opening hours formats
        let daySchedule = null

        if (typeof openingHours === 'object' && openingHours !== null) {
          // Handle object format like { monday: { open: "09:00", close: "17:00" } }
          const dayNames = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday']
          const fullDayName = dayNames[currentDay]
          daySchedule = openingHours[fullDayName]
        }

        if (!daySchedule) return true // If no schedule for today, assume open

        // Check if the business is closed today
        if (daySchedule.closed || !daySchedule.open || !daySchedule.close) {
          return false
        }

        const openTime = daySchedule.open
        const closeTime = daySchedule.close

        // Compare current time with opening hours
        if (currentTime >= openTime && currentTime <= closeTime) {
          return true
        }

        return false
      } catch (error) {
        console.error('Error checking opening hours:', error)
        return true // Default to open if there's an error
      }
    })()

    if (!isWithinOpeningHours) {
      return {
        isAvailable: false,
        status: "closed",
        message: "Closed",
        showOverlay: true
      }
    }

    // Check service availability based on customer's delivery mode preference
    const businessDeliveryAvailable = deliveryAvailable ?? false
    const businessPickupAvailable = pickupAvailable ?? true

    if (mode === "delivery") {
      if (!businessDeliveryAvailable && businessPickupAvailable) {
        return {
          isAvailable: true,
          status: "pickup_only",
          message: "Pickup only",
          showOverlay: false // Show as available but with pickup only badge
        }
      } else if (!businessDeliveryAvailable && !businessPickupAvailable) {
        return {
          isAvailable: false,
          status: "no_service",
          message: "Service unavailable",
          showOverlay: true
        }
      } else if (businessDeliveryAvailable && userPostcode) {
        // Check if delivery is available to user's postcode
        if (isDeliveryAvailableToPostcode === false) {
          return {
            isAvailable: true,
            status: "pickup_only",
            message: "Out of delivery range",
            showOverlay: false // Show as available but with pickup only badge
          }
        } else if (isDeliveryAvailableToPostcode === true) {
          return {
            isAvailable: true,
            status: "open",
            message: "Available for delivery",
            showOverlay: false
          }
        } else {
          // Still loading delivery availability check
          return {
            isAvailable: true,
            status: "open",
            message: "Available for delivery",
            showOverlay: false
          }
        }
      } else if (businessDeliveryAvailable && !userPostcode) {
        return {
          isAvailable: true,
          status: "open",
          message: "Available for delivery",
          showOverlay: false
        }
      }
    } else if (mode === "pickup") {
      if (!businessPickupAvailable && businessDeliveryAvailable) {
        return {
          isAvailable: true,
          status: "delivery_unavailable",
          message: "Delivery only",
          showOverlay: false // Show as available but with delivery only badge
        }
      } else if (!businessPickupAvailable && !businessDeliveryAvailable) {
        return {
          isAvailable: false,
          status: "no_service",
          message: "Currently unavailable",
          showOverlay: true
        }
      } else if (businessPickupAvailable) {
        return {
          isAvailable: true,
          status: "open",
          message: "Available for pickup",
          showOverlay: false
        }
      }
    }

    // Default to available
    return {
      isAvailable: true,
      status: "open",
      message: "Open",
      showOverlay: false
    }
  }

  const status = getBusinessStatus()

  // Card variant - overlay on business cards
  if (variant === "card") {
    return (
      <>
        {/* Overlay for unavailable businesses */}
        {status.showOverlay && (
          <div className={cn(
            "absolute inset-0 bg-gray-900/60 rounded-lg flex items-center justify-center z-10",
            className
          )}>
            <div className="text-center text-white p-2 max-w-[90%]">
              <div className="flex items-center justify-center mb-1">
                {status.status === "closed" ? (
                  <Clock className="h-5 w-5" />
                ) : (
                  <AlertCircle className="h-5 w-5" />
                )}
              </div>
              <p className="font-medium text-xs sm:text-sm leading-tight">{status.message}</p>
            </div>
          </div>
        )}

        {/* Status badges for available businesses with limited service */}
        {!status.showOverlay && status.status === "pickup_only" && (
          <div className="absolute top-2 left-2 z-10">
            <Badge variant="secondary" className="bg-orange-100 text-orange-800 border-orange-200">
              <Store className="h-3 w-3 mr-1" />
              Pickup only
            </Badge>
          </div>
        )}

        {!status.showOverlay && status.status === "delivery_unavailable" && (
          <div className="absolute top-2 left-2 z-10">
            <Badge variant="secondary" className="bg-blue-100 text-blue-800 border-blue-200">
              <Truck className="h-3 w-3 mr-1" />
              Delivery only
            </Badge>
          </div>
        )}
      </>
    )
  }

  // Page variant - status indicator for business pages
  if (variant === "page") {
    if (!status.isAvailable) {
      return (
        <div className={cn(
          "bg-red-50 border border-red-200 rounded-lg p-4 mb-4",
          className
        )}>
          <div className="flex items-center gap-3">
            <div className="flex-shrink-0">
              {status.status === "closed" ? (
                <Clock className="h-5 w-5 text-red-600" />
              ) : (
                <AlertCircle className="h-5 w-5 text-red-600" />
              )}
            </div>
            <div>
              <h3 className="font-medium text-red-900">
                {status.status === "closed" ? "Currently Closed" : "Service Unavailable"}
              </h3>
              <p className="text-sm text-red-700 mt-1">{status.message}</p>
            </div>
          </div>
        </div>
      )
    }

    if (status.status === "pickup_only") {
      return (
        <div className={cn(
          "bg-orange-50 border border-orange-200 rounded-lg p-4 mb-4",
          className
        )}>
          <div className="flex items-center gap-3">
            <div className="flex-shrink-0">
              <Store className="h-5 w-5 text-orange-600" />
            </div>
            <div>
              <h3 className="font-medium text-orange-900">Pickup Only</h3>
              <p className="text-sm text-orange-700 mt-1">
                This business is currently only available for pickup orders.
              </p>
            </div>
          </div>
        </div>
      )
    }

    if (status.status === "delivery_unavailable") {
      return (
        <div className={cn(
          "bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4",
          className
        )}>
          <div className="flex items-center gap-3">
            <div className="flex-shrink-0">
              <Truck className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <h3 className="font-medium text-blue-900">Delivery Only</h3>
              <p className="text-sm text-blue-700 mt-1">
                This business is currently only available for delivery orders.
              </p>
            </div>
          </div>
        </div>
      )
    }

    // For available businesses, don't show anything
    return null
  }

  return null
}
