"use client"

import { Clock, Truck, Store, AlertCircle } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"
import { useDeliveryMode } from "@/context/delivery-mode-context"

interface BusinessStatusOverlayProps {
  businessId: string
  businessName?: string
  isTemporarilyClosed?: boolean
  closureMessage?: string
  deliveryAvailable?: boolean
  pickupAvailable?: boolean
  openingHours?: any
  userPostcode?: string
  className?: string
  variant?: "card" | "page"
}

interface BusinessStatus {
  isAvailable: boolean
  status: "open" | "closed" | "pickup_only" | "delivery_unavailable" | "no_service"
  message: string
  showOverlay: boolean
}

export default function BusinessStatusOverlay({
  businessId,
  businessName,
  isTemporarilyClosed,
  closureMessage,
  deliveryAvailable,
  pickupAvailable,
  openingHours,
  userPostcode,
  className,
  variant = "card"
}: BusinessStatusOverlayProps) {
  const { mode } = useDeliveryMode()

  // Determine business status based on current conditions
  const getBusinessStatus = (): BusinessStatus => {
    // Check if business is temporarily closed
    if (isTemporarilyClosed) {
      return {
        isAvailable: false,
        status: "closed",
        message: closureMessage || "Temporarily closed",
        showOverlay: true
      }
    }

    // Check opening hours (simplified - would need actual opening hours logic)
    // For now, assume business is open during normal hours
    const isWithinOpeningHours = true // TODO: Implement actual opening hours check

    if (!isWithinOpeningHours) {
      return {
        isAvailable: false,
        status: "closed",
        message: "Closed",
        showOverlay: true
      }
    }

    // Check service availability based on customer's delivery mode preference
    const businessDeliveryAvailable = deliveryAvailable ?? false
    const businessPickupAvailable = pickupAvailable ?? true

    if (mode === "delivery") {
      if (!businessDeliveryAvailable && businessPickupAvailable) {
        return {
          isAvailable: true,
          status: "pickup_only",
          message: "Pickup only",
          showOverlay: false // Show as available but with pickup only badge
        }
      } else if (!businessDeliveryAvailable && !businessPickupAvailable) {
        return {
          isAvailable: false,
          status: "no_service",
          message: "Service unavailable",
          showOverlay: true
        }
      } else if (businessDeliveryAvailable && userPostcode) {
        // TODO: Check if delivery is available to user's postcode
        // For now, assume delivery is available if business offers it
        return {
          isAvailable: true,
          status: "open",
          message: "Available for delivery",
          showOverlay: false
        }
      } else if (businessDeliveryAvailable && !userPostcode) {
        return {
          isAvailable: true,
          status: "open",
          message: "Available for delivery",
          showOverlay: false
        }
      }
    } else if (mode === "pickup") {
      if (!businessPickupAvailable) {
        return {
          isAvailable: false,
          status: "no_service",
          message: "Pickup not available",
          showOverlay: true
        }
      } else {
        return {
          isAvailable: true,
          status: "open",
          message: "Available for pickup",
          showOverlay: false
        }
      }
    }

    // Default to available
    return {
      isAvailable: true,
      status: "open",
      message: "Open",
      showOverlay: false
    }
  }

  const status = getBusinessStatus()

  // Card variant - overlay on business cards
  if (variant === "card") {
    return (
      <>
        {/* Overlay for unavailable businesses */}
        {status.showOverlay && (
          <div className={cn(
            "absolute inset-0 bg-gray-900/60 rounded-lg flex items-center justify-center z-10",
            className
          )}>
            <div className="text-center text-white p-4">
              <div className="flex items-center justify-center mb-2">
                {status.status === "closed" ? (
                  <Clock className="h-6 w-6" />
                ) : (
                  <AlertCircle className="h-6 w-6" />
                )}
              </div>
              <p className="font-medium text-sm">{status.message}</p>
            </div>
          </div>
        )}

        {/* Status badges for available businesses with limited service */}
        {!status.showOverlay && status.status === "pickup_only" && (
          <div className="absolute top-2 left-2 z-10">
            <Badge variant="secondary" className="bg-orange-100 text-orange-800 border-orange-200">
              <Store className="h-3 w-3 mr-1" />
              Pickup only
            </Badge>
          </div>
        )}
      </>
    )
  }

  // Page variant - status indicator for business pages
  if (variant === "page") {
    if (!status.isAvailable) {
      return (
        <div className={cn(
          "bg-red-50 border border-red-200 rounded-lg p-4 mb-4",
          className
        )}>
          <div className="flex items-center gap-3">
            <div className="flex-shrink-0">
              {status.status === "closed" ? (
                <Clock className="h-5 w-5 text-red-600" />
              ) : (
                <AlertCircle className="h-5 w-5 text-red-600" />
              )}
            </div>
            <div>
              <h3 className="font-medium text-red-900">
                {status.status === "closed" ? "Currently Closed" : "Service Unavailable"}
              </h3>
              <p className="text-sm text-red-700 mt-1">{status.message}</p>
            </div>
          </div>
        </div>
      )
    }

    if (status.status === "pickup_only") {
      return (
        <div className={cn(
          "bg-orange-50 border border-orange-200 rounded-lg p-4 mb-4",
          className
        )}>
          <div className="flex items-center gap-3">
            <div className="flex-shrink-0">
              <Store className="h-5 w-5 text-orange-600" />
            </div>
            <div>
              <h3 className="font-medium text-orange-900">Pickup Only</h3>
              <p className="text-sm text-orange-700 mt-1">
                This business is currently only available for pickup orders.
              </p>
            </div>
          </div>
        </div>
      )
    }

    // For available businesses, don't show anything
    return null
  }

  return null
}
