import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { checkBusinessAvailability } from '@/lib/business-availability'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

export async function POST(request: NextRequest) {
  try {
    const { businessIds } = await request.json()
    
    if (!businessIds || !Array.isArray(businessIds)) {
      return NextResponse.json(
        { error: 'businessIds array is required' },
        { status: 400 }
      )
    }
    
    // Create Supabase client
    const supabase = createClient(supabaseUrl, supabaseServiceKey)
    
    // Fetch business data
    const { data: businesses, error } = await supabase
      .from('businesses')
      .select('id, name, is_temporarily_closed, closure_message, opening_hours')
      .in('id', businessIds)
    
    if (error) {
      console.error('Error fetching businesses:', error)
      return NextResponse.json(
        { error: 'Failed to fetch business data' },
        { status: 500 }
      )
    }
    
    // Check availability for each business
    const availabilityChecks: Record<string, any> = {}
    
    for (const business of businesses || []) {
      const check = checkBusinessAvailability(
        business.id.toString(),
        business.name,
        business.is_temporarily_closed,
        business.closure_message,
        business.opening_hours
      )
      
      availabilityChecks[business.id.toString()] = check
    }
    
    return NextResponse.json({
      businesses: availabilityChecks
    })
    
  } catch (error) {
    console.error('Error in business availability API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
