-- Add temporary availability fields to business_delivery_config
-- These fields allow businesses to temporarily disable services without losing configuration

-- Add temporary availability fields
ALTER TABLE business_delivery_config 
ADD COLUMN IF NOT EXISTS delivery_available BOOLEAN DEFAULT true,
ADD COLUMN IF NOT EXISTS pickup_available BOOLEAN DEFAULT true;

-- Add comments to clarify the difference between enabled vs available
COMMENT ON COLUMN business_delivery_config.delivery_enabled IS 'Permanent configuration: business has delivery service set up';
COMMENT ON COLUMN business_delivery_config.delivery_available IS 'Temporary availability: can be toggled off for sick drivers, etc.';
COMMENT ON COLUMN business_delivery_config.pickup_enabled IS 'Permanent configuration: business allows pickup';
COMMENT ON COLUMN business_delivery_config.pickup_available IS 'Temporary availability: can be toggled off temporarily';

-- Update existing records to have availability = true by default
UPDATE business_delivery_config 
SET delivery_available = true, pickup_available = true 
WHERE delivery_available IS NULL OR pickup_available IS NULL;

-- Make the fields NOT NULL after setting defaults
ALTER TABLE business_delivery_config 
ALTER COLUMN delivery_available SET NOT NULL,
ALTER COLUMN pickup_available SET NOT NULL;
