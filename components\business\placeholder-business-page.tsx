"use client"

import { useState } from "react"
import { MapPin, Phone, Clock, Star, AlertCircle, Heart, MapIcon, Info, Send, LogIn } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { useToast } from "@/hooks/use-toast"
import { useAuth } from "@/context/unified-auth-context"
import FallbackImage from "@/components/fallback-image"
import ClickablePhone from "@/components/clickable-phone"
import Link from "next/link"

interface PlaceholderBusinessPageProps {
  business: any
}

export default function PlaceholderBusinessPage({ business }: PlaceholderBusinessPageProps) {
  const [showRequestDialog, setShowRequestDialog] = useState(false)
  const [showMapDialog, setShowMapDialog] = useState(false)
  const [showInfoDialog, setShowInfoDialog] = useState(false)
  const [showLoginPrompt, setShowLoginPrompt] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [formData, setFormData] = useState({
    notes: ''
  })
  const { toast } = useToast()
  const { user, userProfile } = useAuth()

  // Helper function to get user display name
  const getUserDisplayName = () => {
    if (userProfile?.name) return userProfile.name
    if (userProfile?.first_name && userProfile?.last_name) {
      return `${userProfile.first_name} ${userProfile.last_name}`
    }
    if (userProfile?.first_name) return userProfile.first_name
    if (user?.email) return user.email.split('@')[0]
    return 'User'
  }

  const handleRequestSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!user) {
      setShowLoginPrompt(true)
      return
    }

    setIsSubmitting(true)

    try {
      const response = await fetch('/api/business-requests', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          businessName: business.name,
          businessType: business.businessTypeName,
          suggestedAddress: business.address,
          customerName: getUserDisplayName(),
          customerEmail: user.email,
          notes: formData.notes
        })
      })

      if (response.ok) {
        toast({
          title: "Request submitted!",
          description: `We'll let ${business.name} know you're interested in ordering through Loop.`,
        })
        setShowRequestDialog(false)
        setFormData({ notes: '' })
      } else {
        throw new Error('Failed to submit request')
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to submit your request. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const formatOpeningHours = () => {
    // For placeholder businesses, show generic message
    return "Hours will be available when this business joins Loop"
  }

  return (
    <div>
      {/* Business Banner */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div className="container-fluid py-6">
          <div className="text-center">
            <div className="inline-flex items-center gap-2 bg-white/20 rounded-full px-4 py-2 mb-4">
              <AlertCircle className="h-5 w-5" />
              <span className="font-medium">Not yet on Loop</span>
            </div>
            <h1 className="text-3xl font-bold mb-2">{business.name}</h1>
            <p className="text-blue-100 text-lg">
              This business isn't taking orders through Loop yet, but you can request them to join!
            </p>
          </div>
        </div>
      </div>

      {/* Business Information */}
      <div className="container-fluid py-6">
        <div className="flex flex-col md:flex-row gap-6">
          {/* Left Column - Business Logo */}
          <div className="w-full md:w-1/3">
            <div className="relative rounded-lg overflow-hidden shadow-md aspect-square bg-white">
              <FallbackImage
                src={business.logo_url || business.image || business.coverImage}
                alt={`${business.name} logo`}
                fallbackSrc="/placeholder.svg"
                className="w-full h-full object-contain p-4"
              />
              <div className="absolute top-4 right-4">
                <Badge className="bg-blue-600 text-white">
                  Not on Loop
                </Badge>
              </div>
            </div>
          </div>

          {/* Right Column - Business Details */}
          <div className="w-full md:w-2/3">
            <div className="flex flex-wrap items-center justify-between mb-4">
              <div>
                <h2 className="text-2xl font-bold">{business.name}</h2>
                {business.businessTypeName && (
                  <div className="mt-1">
                    <Badge variant="outline" className="bg-emerald-50 text-emerald-700 border-emerald-200">
                      {business.businessTypeName}
                    </Badge>
                  </div>
                )}
              </div>

              {/* Action Buttons */}
              <div className="flex gap-2 mt-2 md:mt-0">
                <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-1 text-sm"
                  onClick={() => setShowInfoDialog(true)}
                >
                  <Info size={14} />
                  More Info
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-1 text-sm"
                  onClick={() => setShowMapDialog(true)}
                >
                  <MapIcon size={14} />
                  View Map
                </Button>
              </div>
            </div>

            {/* Business Attributes */}
            {(() => {
              let attributes: string[] = [];
              if (business.cuisines && business.cuisines.length > 0) {
                attributes = business.cuisines;
              } else if (business.storeTypes && business.storeTypes.length > 0) {
                attributes = business.storeTypes;
              } else if (business.pharmacyTypes && business.pharmacyTypes.length > 0) {
                attributes = business.pharmacyTypes;
              } else if (business.cafeTypes && business.cafeTypes.length > 0) {
                attributes = business.cafeTypes;
              }

              return attributes.length > 0 ? (
                <div className="flex flex-wrap gap-2 mb-4">
                  {attributes.map((attribute: string) => (
                    <span key={attribute} className="bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded-full">
                      {attribute}
                    </span>
                  ))}
                </div>
              ) : null;
            })()}

            {/* Rating and Location */}
            <div className="flex flex-wrap items-center gap-4 mb-4 text-sm">
              {business.rating && (
                <div className="flex items-center">
                  <Star className="h-4 w-4 text-yellow-400 fill-yellow-400 mr-1" />
                  <span className="font-medium">{business.rating.toFixed(1)}</span>
                  {business.reviewCount && (
                    <span className="text-gray-500 ml-1">({business.reviewCount})</span>
                  )}
                </div>
              )}

              <div className="flex items-center">
                <MapPin className="h-4 w-4 text-rose-400 mr-1" />
                <span>{business.location}, Jersey</span>
              </div>

              {business.phone && (
                <div className="flex items-center">
                  <ClickablePhone phoneNumber={business.phone} />
                </div>
              )}
            </div>

            {/* Description */}
            {business.description && (
              <div className="mb-4">
                <p className="text-gray-600">{business.description}</p>
              </div>
            )}

            {/* Opening Hours */}
            <div className="flex items-center gap-2 mb-6 text-sm">
              <Clock className="h-4 w-4 text-gray-500" />
              <span>{formatOpeningHours()}</span>
            </div>

            {/* Request Business Card */}
            <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-blue-800">
                  <Heart className="h-5 w-5" />
                  Want {business.name} on Loop?
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-blue-700 mb-4">
                  Let us know you're interested and we'll reach out to {business.name} to encourage them to join Loop Jersey!
                </p>
                <Button
                  onClick={() => {
                    if (!user) {
                      setShowLoginPrompt(true)
                    } else {
                      setShowRequestDialog(true)
                    }
                  }}
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                >
                  <Send className="h-4 w-4 mr-2" />
                  {user ? 'Request This Business' : 'Login to Request'}
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Menu Categories Preview */}
        {business.menuCategories && business.menuCategories.length > 0 && (
          <div className="mt-8">
            <h3 className="text-xl font-bold mb-4">Menu Categories</h3>
            <p className="text-gray-600 mb-4">
              Here's what you could expect if {business.name} joins Loop:
            </p>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {business.menuCategories.map((category: any) => (
                <Card key={category.id} className="text-center p-4 bg-gray-50 border-gray-200">
                  <h4 className="font-medium text-gray-800">{category.name}</h4>
                  {category.description && (
                    <p className="text-xs text-gray-600 mt-1">{category.description}</p>
                  )}
                </Card>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Request Business Dialog */}
      <Dialog open={showRequestDialog} onOpenChange={setShowRequestDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Request {business.name}</DialogTitle>
            <DialogDescription>
              Help us bring {business.name} to Loop by showing your interest!
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleRequestSubmit} className="space-y-4">
            {user && (
              <div className="bg-blue-50 p-3 rounded-lg">
                <p className="text-sm text-blue-700">
                  Submitting as: <strong>{getUserDisplayName()}</strong> ({user.email})
                </p>
              </div>
            )}
            <div>
              <Label htmlFor="notes">Why do you want {business.name} on Loop? (Optional)</Label>
              <Textarea
                id="notes"
                value={formData.notes}
                onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                placeholder="Tell us why this business would be great on Loop..."
                rows={3}
              />
            </div>
            <div className="flex gap-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => setShowRequestDialog(false)}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
                className="flex-1 bg-blue-600 hover:bg-blue-700"
              >
                {isSubmitting ? 'Submitting...' : 'Submit Request'}
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>

      {/* Info Dialog */}
      <Dialog open={showInfoDialog} onOpenChange={setShowInfoDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>{business.name} Information</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <h4 className="font-medium mb-2">Address</h4>
              <p className="text-sm text-gray-600">{business.address}</p>
              <p className="text-sm text-gray-600">{business.location}, Jersey</p>
            </div>

            {business.phone && (
              <div>
                <h4 className="font-medium mb-2">Phone</h4>
                <ClickablePhone phoneNumber={business.phone} />
              </div>
            )}

            <div>
              <h4 className="font-medium mb-2">Opening Hours</h4>
              <div className="text-sm text-gray-600 space-y-1">
                <div className="text-gray-500">
                  Opening hours will be available when this business joins Loop
                </div>
              </div>
            </div>

            {business.hygiene_rating && (
              <div>
                <h4 className="font-medium mb-2">Food Hygiene Rating</h4>
                <div className="flex items-center gap-2">
                  <span className="text-2xl font-bold text-green-600">{business.hygiene_rating}</span>
                  <span className="text-sm text-gray-600">/ 5</span>
                </div>
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Map Dialog */}
      <Dialog open={showMapDialog} onOpenChange={setShowMapDialog}>
        <DialogContent className="sm:max-w-3xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <MapIcon size={18} className="text-emerald-600" />
              {business.name} Location
            </DialogTitle>
          </DialogHeader>
          <div className="py-2">
            <div className="bg-gray-100 rounded-lg p-8 text-center">
              <MapIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600">Interactive map will be available if {business.name} joins Loop</p>
              <p className="text-sm text-gray-500 mt-2">
                Location: {business.address}, {business.location}, Jersey
              </p>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Login Prompt Dialog */}
      <Dialog open={showLoginPrompt} onOpenChange={setShowLoginPrompt}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <LogIn className="h-5 w-5" />
              Login Required
            </DialogTitle>
            <DialogDescription>
              You need to be logged in to request businesses to join Loop.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="bg-blue-50 p-4 rounded-lg">
              <h4 className="font-medium text-blue-800 mb-2">Why do I need to login?</h4>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• Prevents duplicate requests</li>
                <li>• Ensures authentic community feedback</li>
                <li>• Allows us to notify you when {business.name} joins</li>
              </ul>
            </div>

            <div className="flex gap-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => setShowLoginPrompt(false)}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                asChild
                className="flex-1 bg-blue-600 hover:bg-blue-700"
              >
                <Link href="/login">
                  <LogIn className="h-4 w-4 mr-2" />
                  Login
                </Link>
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
