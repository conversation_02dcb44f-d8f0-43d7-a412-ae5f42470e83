"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Truck, Store, MapPin, Clock, DollarSign } from "lucide-react"
import { useDeliveryMode } from "@/context/delivery-mode-context"
import { useLocation } from "@/context/location-context"
import { cn } from "@/lib/utils"

interface BusinessDeliveryToggleProps {
  business: {
    id: string
    name: string
    delivery_available?: boolean
    pickup_available?: boolean
    delivery_fee?: number
    delivery_radius?: number
    preparation_time_minutes?: number
  }
  selectedMethod?: "delivery" | "pickup"
  onMethodChange?: (method: "delivery" | "pickup") => void
  className?: string
}

export default function BusinessDeliveryToggle({ 
  business, 
  selectedMethod,
  onMethodChange,
  className 
}: BusinessDeliveryToggleProps) {
  const { mode: globalMode } = useDeliveryMode()
  const { postcode, coordinates } = useLocation()
  
  // Use selectedMethod if provided, otherwise fall back to global mode
  const currentMethod = selectedMethod || globalMode
  
  // Determine available options
  const deliveryAvailable = business.delivery_available ?? false
  const pickupAvailable = business.pickup_available ?? true
  
  // Calculate delivery info
  const deliveryFee = business.delivery_fee || 2.50
  const prepTime = business.preparation_time_minutes || 15
  const deliveryTime = prepTime + 20 // Add delivery time to prep time

  // Handle method change
  const handleMethodChange = (method: "delivery" | "pickup") => {
    onMethodChange?.(method)
  }

  // If only one option is available, don't show toggle
  if (!deliveryAvailable && pickupAvailable) {
    return (
      <div className={cn("bg-white rounded-lg shadow-sm p-4 border border-gray-100", className)}>
        <div className="text-center">
          <div className="flex items-center justify-center mb-3">
            <Store className="h-5 w-5 text-orange-600 mr-2" />
            <span className="font-medium text-gray-900">Pickup Only</span>
          </div>
          <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
            <div className="flex items-center justify-center mb-2">
              <Store className="h-6 w-6 text-orange-600" />
            </div>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm text-orange-600">Ready in</span>
                <span className="font-medium text-orange-900">{prepTime} min</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-orange-600">Fee</span>
                <span className="font-medium text-orange-900">Free</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (deliveryAvailable && !pickupAvailable) {
    return (
      <div className={cn("bg-white rounded-lg shadow-sm p-4 border border-gray-100", className)}>
        <div className="text-center">
          <div className="flex items-center justify-center mb-3">
            <Truck className="h-5 w-5 text-emerald-600 mr-2" />
            <span className="font-medium text-gray-900">Delivery Only</span>
          </div>
          <div className="bg-emerald-50 border border-emerald-200 rounded-lg p-4">
            <div className="flex items-center justify-center mb-2">
              <Truck className="h-6 w-6 text-emerald-600" />
            </div>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm text-emerald-600">Delivered in</span>
                <span className="font-medium text-emerald-900">{deliveryTime} min</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-emerald-600">Delivery fee</span>
                <span className="font-medium text-emerald-900">£{deliveryFee.toFixed(2)}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Both options available - show toggle
  return (
    <div className={cn("bg-white rounded-lg shadow-sm p-4 border border-gray-100", className)}>
      <div className="mb-4">
        <div className="flex items-center justify-center mb-4">
          <h3 className="text-lg font-medium text-gray-900">
            {currentMethod === 'delivery' ? 'Delivery selected' : 'Pickup selected'}
          </h3>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Pickup Option */}
          <div
            className={cn(
              "cursor-pointer rounded-lg border p-6 transition-all duration-200",
              currentMethod === 'pickup'
                ? 'border-orange-400 bg-orange-50 shadow-md'
                : 'border-gray-200 bg-white hover:border-orange-300'
            )}
            onClick={() => handleMethodChange('pickup')}
          >
            <div className="flex items-center justify-center mb-4">
              <Store className={cn(
                "h-6 w-6 mr-2",
                currentMethod === 'pickup' ? 'text-orange-600' : 'text-gray-600'
              )} />
              <span className={cn(
                "text-xl font-semibold",
                currentMethod === 'pickup' ? 'text-orange-900' : 'text-gray-900'
              )}>
                Pickup
              </span>
            </div>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className={cn(
                  "text-sm",
                  currentMethod === 'pickup' ? 'text-orange-600' : 'text-gray-600'
                )}>
                  Ready in
                </span>
                <span className={cn(
                  "font-medium",
                  currentMethod === 'pickup' ? 'text-orange-900' : 'text-gray-900'
                )}>
                  {prepTime} min
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className={cn(
                  "text-sm",
                  currentMethod === 'pickup' ? 'text-orange-600' : 'text-gray-600'
                )}>
                  Fee
                </span>
                <span className={cn(
                  "font-medium",
                  currentMethod === 'pickup' ? 'text-orange-900' : 'text-gray-900'
                )}>
                  Free
                </span>
              </div>
            </div>
          </div>

          {/* Delivery Option */}
          <div
            className={cn(
              "cursor-pointer rounded-lg border p-6 transition-all duration-200",
              currentMethod === 'delivery'
                ? 'border-emerald-400 bg-emerald-50 shadow-md'
                : 'border-gray-200 bg-white hover:border-emerald-300'
            )}
            onClick={() => handleMethodChange('delivery')}
          >
            <div className="flex items-center justify-center mb-4">
              <Truck className={cn(
                "h-6 w-6 mr-2",
                currentMethod === 'delivery' ? 'text-emerald-600' : 'text-gray-600'
              )} />
              <span className={cn(
                "text-xl font-semibold",
                currentMethod === 'delivery' ? 'text-emerald-900' : 'text-gray-900'
              )}>
                Delivery
              </span>
            </div>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className={cn(
                  "text-sm",
                  currentMethod === 'delivery' ? 'text-emerald-600' : 'text-gray-600'
                )}>
                  Delivered in
                </span>
                <span className={cn(
                  "font-medium",
                  currentMethod === 'delivery' ? 'text-emerald-900' : 'text-gray-900'
                )}>
                  {deliveryTime} min
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className={cn(
                  "text-sm",
                  currentMethod === 'delivery' ? 'text-emerald-600' : 'text-gray-600'
                )}>
                  Delivery fee
                </span>
                <span className={cn(
                  "font-medium",
                  currentMethod === 'delivery' ? 'text-emerald-900' : 'text-gray-900'
                )}>
                  £{deliveryFee.toFixed(2)}
                </span>
              </div>
              {postcode && (
                <div className="flex justify-between items-center">
                  <span className={cn(
                    "text-sm",
                    currentMethod === 'delivery' ? 'text-emerald-600' : 'text-gray-600'
                  )}>
                    To
                  </span>
                  <span className={cn(
                    "font-medium text-xs",
                    currentMethod === 'delivery' ? 'text-emerald-900' : 'text-gray-900'
                  )}>
                    {postcode}
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
