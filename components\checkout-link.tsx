"use client"

import { useState, useMemo, useEffect } from 'react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { useSaveCartToDatabase } from '@/hooks/use-save-cart-to-database'
import { useRealtimeCart } from '@/context/realtime-cart-context'
import { fetchBusinessAvailability, type BusinessAvailabilityCheck } from '@/lib/business-availability'

interface CheckoutLinkProps {
  className?: string
  buttonClassName?: string
  children?: React.ReactNode
  disabled?: boolean
}

/**
 * A wrapper component for the checkout link that saves cart data to the database
 * before navigating to the checkout page
 */
export default function CheckoutLink({
  className,
  buttonClassName,
  children = "Checkout",
  disabled = false
}: CheckoutLinkProps) {
  const [isSaving, setIsSaving] = useState(false)
  const { saveCartToDatabase } = useSaveCartToDatabase()
  const { cart } = useRealtimeCart()
  const router = useRouter()
  const [businessAvailability, setBusinessAvailability] = useState<Record<string, BusinessAvailabilityCheck>>({})
  const [isCheckingAvailability, setIsCheckingAvailability] = useState(false)

  // Get unique business IDs from cart
  const businessIds = useMemo(() => {
    const ids = new Set<string>()
    cart.forEach(item => {
      ids.add(item.businessId.toString())
    })
    return Array.from(ids)
  }, [cart])

  // Fetch business availability when cart changes
  useEffect(() => {
    if (businessIds.length === 0) {
      setBusinessAvailability({})
      return
    }

    setIsCheckingAvailability(true)
    fetchBusinessAvailability(businessIds)
      .then(availability => {
        setBusinessAvailability(availability)
      })
      .catch(error => {
        console.error('Error checking business availability:', error)
        // On error, assume all businesses are available
        setBusinessAvailability({})
      })
      .finally(() => {
        setIsCheckingAvailability(false)
      })
  }, [businessIds])

  // Check if any businesses in cart are closed
  const closedBusinessCheck = useMemo(() => {
    const closedBusinesses: string[] = []

    Object.values(businessAvailability).forEach(check => {
      if (!check.isAvailable) {
        closedBusinesses.push(check.businessName)
      }
    })

    return {
      hasClosedBusinesses: closedBusinesses.length > 0,
      closedBusinesses,
      reasons: Object.values(businessAvailability)
        .filter(check => !check.isAvailable)
        .map(check => `${check.businessName}: ${check.reason}`)
    }
  }, [businessAvailability])

  const isDisabledDueToClosedBusiness = closedBusinessCheck.hasClosedBusinesses

  const handleClick = async (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault()

    if (disabled || isSaving || isDisabledDueToClosedBusiness) return

    setIsSaving(true)

    try {
      console.log("CHECKOUT-BUTTON: Starting checkout process");

      // Save cart data to database before navigating
      // Set a timeout to prevent getting stuck in the saving state
      const savePromise = saveCartToDatabase();

      // Create a timeout promise that resolves after 8 seconds
      // Increased from 5 to 8 seconds to give more time for database operations
      const timeoutPromise = new Promise<boolean>(resolve => {
        setTimeout(() => {
          console.log("CHECKOUT-BUTTON: Timeout reached, proceeding with navigation");
          resolve(true);
        }, 8000);
      });

      // Race the save operation against the timeout
      // This ensures we don't get stuck if the save operation hangs
      const result = await Promise.race([
        savePromise.then(success => {
          console.log("CHECKOUT-BUTTON: Cart save completed with result:", success);
          return success;
        }),
        timeoutPromise
      ]);

      console.log("CHECKOUT-BUTTON: Save operation result:", result);

      // Add a small delay before navigation to ensure any pending state updates are complete
      setTimeout(() => {
        // Navigate to checkout page
        console.log("CHECKOUT-BUTTON: Navigating to checkout page");
        router.push('/checkout');
      }, 100);
    } catch (error) {
      console.error("CHECKOUT-BUTTON: Error saving cart to database:", error);

      // Add a small delay before navigation to ensure any pending state updates are complete
      setTimeout(() => {
        // Navigate anyway, even if saving fails
        console.log("CHECKOUT-BUTTON: Error occurred, but still navigating to checkout page");
        router.push('/checkout');
      }, 100);
    } finally {
      // Set isSaving to false after a short delay to ensure it's still true during navigation
      // This prevents multiple clicks during the navigation process
      setTimeout(() => {
        setIsSaving(false);
      }, 200);
    }
  }

  if (disabled || isDisabledDueToClosedBusiness || isCheckingAvailability) {
    return (
      <Button
        className={buttonClassName}
        disabled={true}
        title={isDisabledDueToClosedBusiness ?
          closedBusinessCheck.reasons.join('\n') :
          isCheckingAvailability ? 'Checking business availability...' :
          undefined
        }
      >
        {isCheckingAvailability ? 'Checking...' :
         isDisabledDueToClosedBusiness ? 'Business Closed' :
         children}
      </Button>
    )
  }

  return (
    <div className={className}>
      <Button
        className={buttonClassName}
        onClick={handleClick}
        disabled={isSaving}
      >
        {isSaving ? "Processing..." : children}
      </Button>
    </div>
  )
}
