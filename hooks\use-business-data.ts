import { useState, useCallback, useEffect, useRef } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/context/unified-auth-context'

interface BusinessData {
  id: number
  name: string
  business_type_id: number
  business_type?: string
  logo_url?: string | null
  description?: string
  address?: string
  postcode?: string
  location?: string
  phone?: string
  delivery_radius?: number
  preparation_time_minutes?: number
  minimum_order_amount?: number
  delivery_fee?: number
  delivery_fee_model?: string
  delivery_fee_per_km?: number
  coordinates?: any
  hygiene_rating?: string
  allergen_info?: string
  attributes?: string[]
  opening_hours?: any
  use_loop_delivery?: boolean
  pickup_available?: boolean
  pickup_asap_available?: boolean
  pickup_scheduled_time_available?: boolean
  pickup_scheduled_period_available?: boolean
  delivery_asap_available?: boolean
  delivery_scheduled_time_available?: boolean
  delivery_scheduled_period_available?: boolean
  min_advance_booking_minutes?: number
  max_advance_booking_days?: number
  banner_url?: string | null
  is_approved?: boolean
  [key: string]: any // Allow for additional fields
}

interface BusinessOption {
  id: number
  name: string
  business_type?: string
}

export function useBusinessData() {
  const router = useRouter()
  const { user, isAdmin, isSuperAdmin, userProfile, isLoading: authLoading } = useAuth()

  // State
  const [business, setBusiness] = useState<BusinessData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isPendingApproval, setIsPendingApproval] = useState(false)

  // Ref to prevent multiple simultaneous calls
  const fetchingRef = useRef(false)

  // Since business-admin is now restricted to business users only,
  // we don't need to check for admin status or manage admin-related state

  // Since business-admin is now restricted to business users only,
  // we don't need to load business ID from localStorage

  // Fetch business data
  const fetchBusinessData = useCallback(async () => {
    // Prevent multiple simultaneous calls
    if (fetchingRef.current) {
      return
    }

    try {
      fetchingRef.current = true
      setIsLoading(true)
      setError(null)

      const token = localStorage.getItem('loop_jersey_auth_token') || ''

      // Since business-admin is now restricted to business users only,
      // we always use the basic business-data endpoint
      const url = '/api/business-admin/business-data'

      const response = await fetch(url, {
        headers: {
          'Authorization': token ? `Bearer ${token}` : '',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      })

      if (!response.ok) {
        // If we get a 401 or 403, redirect to login
        if (response.status === 401 || response.status === 403) {
          router.push("/login?redirectTo=/business-admin/dashboard")
          return
        }

        throw new Error(`API error: ${response.status}`)
      }

      const data = await response.json()

      if (data.business) {
        // Set the complete business data instead of just a subset
        setBusiness(data.business)

        // Check if the business is pending approval
        setIsPendingApproval(data.business.is_approved === false)
      } else {
        setError("No business data found")
      }
    } catch (err) {
      console.error("Error fetching business data:", err)
      setError("An unexpected error occurred")
    } finally {
      setIsLoading(false)
      fetchingRef.current = false
    }
  }, [router])

  // Since business-admin is now restricted to business users only,
  // we don't need to fetch available businesses or handle business changes

  // Initialize data loading
  useEffect(() => {
    if (user && !authLoading) {
      // Since business-admin section is now restricted to business users only,
      // we always fetch business data immediately for authenticated users
      fetchBusinessData()
    }
  }, [user?.id, authLoading]) // Remove fetchBusinessData from dependencies to prevent infinite re-renders

  return {
    business,
    availableBusinesses: [], // Empty array since business-admin is for business users only
    selectedBusinessId: null, // Always null since no business selection needed
    // Removed isAdminUser since business-admin is now restricted to business users only
    isLoading,
    error,
    isPendingApproval,
    handleBusinessChange: () => {}, // No-op function since no business selection needed
    fetchBusinessData,
    fetchAvailableBusinesses: () => {} // No-op function since not needed
  }
}
