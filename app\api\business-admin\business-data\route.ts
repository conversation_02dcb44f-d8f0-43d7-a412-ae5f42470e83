import { NextResponse } from "next/server";
import { createClient } from "@supabase/supabase-js";
import { verifyBusinessAdminAccess } from "@/lib/simple-auth";

// Create a Supabase client with the service role key for admin access
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const adminClient = createClient(supabaseUrl, supabaseServiceKey);

export async function GET(request: Request) {
  try {
    console.log("Business data API called");

    // Use the same auth pattern as super-admin routes
    const accessCheck = await verifyBusinessAdminAccess(request)
    if (!accessCheck.authorized) {
      return NextResponse.json(
        { error: accessCheck.error },
        { status: accessCheck.status || 401 }
      )
    }

    // Parse the URL to get query parameters
    const url = new URL(request.url);
    const businessId = url.searchParams.get('businessId');

    console.log("Business data API called with businessId:", businessId);

    const userProfile = accessCheck.profile;

    console.log("User profile:", {
      id: userProfile.id,
      email: userProfile.email,
      role: userProfile.role
    });

    // Check if user is admin or super admin
    const isAdmin = userProfile.role === "admin" || userProfile.role === "super_admin";

    // Determine which business to fetch data for
    let targetBusinessId: number | null = null;

    if (businessId) {
      // Admin user requesting specific business
      if (!isAdmin) {
        return NextResponse.json(
          { error: "Only admins can specify business ID" },
          { status: 403 }
        );
      }
      targetBusinessId = parseInt(businessId);
    } else {
      // Regular user - get their associated business from business_managers table
      if (!isAdmin) {
        // Fetch business association from business_managers table
        const { data: managerData, error: managerError } = await adminClient
          .from("business_managers")
          .select("business_id")
          .eq("user_id", userProfile.id)
          .maybeSingle();

        if (managerError) {
          console.error("Error fetching business manager data:", managerError);
          return NextResponse.json(
            { error: "Error fetching business association" },
            { status: 500 }
          );
        }

        if (!managerData || !managerData.business_id) {
          return NextResponse.json(
            { error: "No business associated with this account" },
            { status: 403 }
          );
        }

        targetBusinessId = managerData.business_id;
      } else {
        // For admin users without a business_id, we need a business ID parameter
        return NextResponse.json(
          { error: "Admin users must specify a business ID parameter" },
          { status: 400 }
        );
      }
    }

    console.log("Fetching business data for business ID:", targetBusinessId);

    // Fetch business data with delivery config from the new table structure
    const { data: business, error: businessError } = await adminClient
      .from("businesses")
      .select(`
        id,
        name,
        slug,
        description,
        address,
        phone,
        logo_url,
        banner_url,
        location,
        coordinates,
        preparation_time_minutes,
        is_featured,
        minimum_order_amount,
        opening_hours,
        postcode,
        is_approved,
        latitude,
        longitude,
        status,
        is_temporarily_closed,
        closure_message,
        hygiene_rating,
        allergen_info,
        business_type_id,
        created_at,
        updated_at,
        business_delivery_config (
          pickup_enabled,
          pickup_available,
          delivery_enabled,
          delivery_available,
          use_loop_delivery,
          restriction_logic,
          delivery_radius,
          pickup_asap_available,
          pickup_scheduled_available,
          delivery_asap_available,
          delivery_scheduled_available,
          min_advance_booking_minutes,
          max_advance_booking_days,
          minimum_order_value,
          maximum_order_value
        )
      `)
      .eq("id", targetBusinessId)
      .single();

    if (businessError) {
      console.error("Error fetching business data:", businessError);
      return NextResponse.json(
        { error: "Failed to fetch business data" },
        { status: 500 }
      );
    }

    if (!business) {
      return NextResponse.json(
        { error: "Business not found" },
        { status: 404 }
      );
    }

    console.log("Successfully fetched business data for:", business.name);

    // Fetch business type name
    let businessTypeName = "Business"; // Default fallback
    console.log("Business type ID:", business.business_type_id);

    if (business.business_type_id) {
      const { data: businessType, error: businessTypeError } = await adminClient
        .from("business_types")
        .select("name")
        .eq("id", business.business_type_id)
        .single();

      console.log("Business type query result:", { businessType, businessTypeError });

      if (businessTypeError) {
        console.error("Error fetching business type:", businessTypeError);
      } else if (businessType) {
        businessTypeName = businessType.name;
        console.log("Found business type name:", businessTypeName);
      }
    } else {
      console.log("No business_type_id found for business");
    }

    // Fetch business attributes
    const { data: businessAttributes, error: attributesError } = await adminClient
      .from("business_attributes")
      .select("attribute_type, attribute_value")
      .eq("business_id", targetBusinessId);

    if (attributesError) {
      console.error("Error fetching business attributes:", attributesError);
      // Continue without attributes - this is not critical
    }

    // Convert attributes to the format expected by the form
    const attributes: string[] = [];
    if (businessAttributes) {
      businessAttributes.forEach(attr => {
        if (attr.attribute_value) {
          attributes.push(attr.attribute_value);
        }
      });
    }

    // Extract delivery config and flatten for backward compatibility
    const deliveryConfig = business.business_delivery_config || {};

    // Add attributes, business type, and flattened delivery config to business object
    const businessWithAttributes = {
      ...business,
      attributes,
      business_type: businessTypeName,
      // Flatten delivery config fields for backward compatibility
      pickup_enabled: deliveryConfig.pickup_enabled,
      pickup_available: deliveryConfig.pickup_available,
      delivery_enabled: deliveryConfig.delivery_enabled,
      delivery_available: deliveryConfig.delivery_available,
      use_loop_delivery: deliveryConfig.use_loop_delivery,
      restriction_logic: deliveryConfig.restriction_logic,
      delivery_radius: deliveryConfig.delivery_radius,
      pickup_asap_available: deliveryConfig.pickup_asap_available,
      pickup_scheduled_available: deliveryConfig.pickup_scheduled_available,
      delivery_asap_available: deliveryConfig.delivery_asap_available,
      delivery_scheduled_available: deliveryConfig.delivery_scheduled_available,
      min_advance_booking_minutes: deliveryConfig.min_advance_booking_minutes,
      max_advance_booking_days: deliveryConfig.max_advance_booking_days,
      minimum_order_value: deliveryConfig.minimum_order_value,
      maximum_order_value: deliveryConfig.maximum_order_value,
      // Keep the nested object for new code
      delivery_config: deliveryConfig
    };

    return NextResponse.json({
      business: businessWithAttributes,
      user: {
        id: userProfile.id,
        email: userProfile.email,
        role: userProfile.role,
        isAdmin
      }
    });

  } catch (error) {
    console.error("Business data API error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
