"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Truck, Store, MapPin } from "lucide-react"
import { useDeliveryMode } from "@/context/delivery-mode-context"
import { useLocation } from "@/context/location-context"
import { cn } from "@/lib/utils"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { UserPostcodeInput } from "@/components/delivery/user-postcode-input"

interface DeliveryModeToggleProps {
  variant?: "homepage" | "header"
  className?: string
  onModeChange?: (mode: "delivery" | "pickup") => void
}

export default function DeliveryModeToggle({ 
  variant = "homepage", 
  className,
  onModeChange 
}: DeliveryModeToggleProps) {
  const { mode, setMode, isHydrated } = useDeliveryMode()
  const { postcode, setPostcode } = useLocation()
  const [showPostcodeDialog, setShowPostcodeDialog] = useState(false)

  // Handle mode change
  const handleModeChange = (newMode: "delivery" | "pickup") => {
    // If switching to delivery and no postcode, show postcode dialog
    if (newMode === "delivery" && !postcode) {
      setShowPostcodeDialog(true)
      return
    }
    
    setMode(newMode)
    onModeChange?.(newMode)
  }

  // Handle postcode submission from dialog
  const handlePostcodeSubmit = (newPostcode: string) => {
    setPostcode(newPostcode)
    setMode("delivery")
    setShowPostcodeDialog(false)
    onModeChange?.("delivery")
  }

  // Don't render until hydrated to prevent hydration mismatch
  if (!isHydrated) {
    return null
  }

  if (variant === "homepage") {
    return (
      <>
        <div className={cn("flex items-center justify-center gap-2 mb-6", className)}>
          <div className="bg-white rounded-full p-1 shadow-lg border border-gray-200">
            <div className="flex">
              <Button
                variant={mode === "delivery" ? "default" : "ghost"}
                size="sm"
                onClick={() => handleModeChange("delivery")}
                className={cn(
                  "rounded-full px-4 py-2 text-sm font-medium transition-all",
                  mode === "delivery" 
                    ? "bg-emerald-600 text-white shadow-sm" 
                    : "text-gray-600 hover:text-gray-900 hover:bg-gray-50"
                )}
              >
                <Truck className="h-4 w-4 mr-2" />
                Delivery
              </Button>
              <Button
                variant={mode === "pickup" ? "default" : "ghost"}
                size="sm"
                onClick={() => handleModeChange("pickup")}
                className={cn(
                  "rounded-full px-4 py-2 text-sm font-medium transition-all",
                  mode === "pickup" 
                    ? "bg-emerald-600 text-white shadow-sm" 
                    : "text-gray-600 hover:text-gray-900 hover:bg-gray-50"
                )}
              >
                <Store className="h-4 w-4 mr-2" />
                Pickup
              </Button>
            </div>
          </div>
        </div>

        {/* Postcode Dialog */}
        <Dialog open={showPostcodeDialog} onOpenChange={setShowPostcodeDialog}>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle>Enter Delivery Location</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <p className="text-sm text-gray-600">
                To use delivery, please enter your Jersey postcode:
              </p>
              <div className="relative">
                <div className="absolute left-3 top-[22px] z-10">
                  <MapPin className="h-4 w-4 text-emerald-500" />
                </div>
                <UserPostcodeInput
                  initialPostcode=""
                  onPostcodeSubmit={handlePostcodeSubmit}
                  buttonText="Set Location"
                  placeholder="Enter your Jersey postcode"
                  className="pl-9"
                />
              </div>
              <p className="text-xs text-gray-500">
                Enter a Jersey postcode (e.g., JE2 3NG) to see delivery options
              </p>
            </div>
          </DialogContent>
        </Dialog>
      </>
    )
  }

  // Header variant - more compact
  return (
    <div className={cn("flex items-center", className)}>
      <div className="bg-gray-50 rounded-lg p-0.5 border border-gray-200">
        <div className="flex">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleModeChange("delivery")}
            className={cn(
              "rounded-md px-3 py-1.5 text-xs font-medium transition-all h-8",
              mode === "delivery" 
                ? "bg-white text-emerald-700 shadow-sm border border-emerald-200" 
                : "text-gray-600 hover:text-gray-900"
            )}
          >
            <Truck className="h-3 w-3 mr-1.5" />
            Delivery
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleModeChange("pickup")}
            className={cn(
              "rounded-md px-3 py-1.5 text-xs font-medium transition-all h-8",
              mode === "pickup" 
                ? "bg-white text-emerald-700 shadow-sm border border-emerald-200" 
                : "text-gray-600 hover:text-gray-900"
            )}
          >
            <Store className="h-3 w-3 mr-1.5" />
            Pickup
          </Button>
        </div>
      </div>

      {/* Postcode Dialog for header variant */}
      <Dialog open={showPostcodeDialog} onOpenChange={setShowPostcodeDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Enter Delivery Location</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <p className="text-sm text-gray-600">
              To use delivery, please enter your Jersey postcode:
            </p>
            <div className="relative">
              <div className="absolute left-3 top-[22px] z-10">
                <MapPin className="h-4 w-4 text-emerald-500" />
              </div>
              <UserPostcodeInput
                initialPostcode=""
                onPostcodeSubmit={handlePostcodeSubmit}
                buttonText="Set Location"
                placeholder="Enter your Jersey postcode"
                className="pl-9"
              />
            </div>
            <p className="text-xs text-gray-500">
              Enter a Jersey postcode (e.g., JE2 3NG) to see delivery options
            </p>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
