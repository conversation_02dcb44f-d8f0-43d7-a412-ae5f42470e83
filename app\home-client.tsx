"use client";

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { But<PERSON> } from "@/components/ui/button";
import { MapPin, AlertTriangle, X } from 'lucide-react';
import { useLocation } from '@/context/location-context';
import { useDeliveryMode } from '@/context/delivery-mode-context';
import {
  Dialog,
  DialogContent,
  DialogTitle,
} from "@/components/ui/dialog";
import { UserPostcodeInput } from '@/components/delivery/user-postcode-input';
import DeliveryModeToggle from '@/components/delivery/delivery-mode-toggle';

const HomeClient: React.FC = () => {
  const router = useRouter();
  const {
    postcode,
    setPostcode
  } = useLocation();
  const { mode } = useDeliveryMode();

  const [validationError, setValidationError] = useState<string | null>(null);
  const [errorDialogOpen, setErrorDialogOpen] = useState(false);
  const [invalidPostcode, setInvalidPostcode] = useState<string>("");

  const handleProceedWithDefault = () => {
    setErrorDialogOpen(false);
    router.push(`/search?postcode=${encodeURIComponent(invalidPostcode)}`);
  };

  const handlePostcodeSubmit = async (postcodeValue: string) => {
    try {
      const response = await fetch('/api/user/location', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ postcode: postcodeValue }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save location');
      }

      setPostcode(postcodeValue);
      router.push(`/search?postcode=${encodeURIComponent(postcodeValue)}&mode=${mode}`);
    } catch (error) {
      console.error('Error saving location:', error);
      setValidationError('Failed to save your location. Please try again.');
      setErrorDialogOpen(true);
    }
  };

  const handleModeChange = (newMode: "delivery" | "pickup") => {
    // Mode change is handled by the DeliveryModeToggle component
    // This callback is for any additional logic if needed
  };

  return (
    <div className="text-center w-full">
      {/* Orders. Delivered Text */}
      <div className="mb-8 md:mb-10 px-4 py-6">
        <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold tracking-tight text-emerald-600" style={{ textShadow: '0 1px 2px rgba(255,255,255,0.8)' }}>
          <span className="inline-block">Orders.</span>{" "}
          <span className="inline-block animate-slide-in-from-right will-change-transform">
            {mode === "pickup" ? "Collected." : "Delivered."}
          </span>
        </h2>
      </div>

      {/* Delivery Mode Toggle */}
      <DeliveryModeToggle
        variant="homepage"
        onModeChange={handleModeChange}
        className="animate-fade-in-delay-1"
      />

      {/* Postcode Entry Bar - Only show for delivery mode */}
      {mode === "delivery" && (
        <div className="w-full max-w-3xl mx-auto animate-fade-in-delay-2">
          <div className="relative">
            <div className="absolute left-4 top-[22px] z-10">
              <MapPin className="h-5 w-5 text-emerald-500" />
            </div>
            <UserPostcodeInput
              initialPostcode={postcode}
              onPostcodeSubmit={handlePostcodeSubmit}
              buttonText="Start"
              placeholder="Enter your postcode for delivery times to your door"
              className="pl-10" // Add left padding for the icon
            />
          </div>

          {/* Postcode Format Hint */}
          <p className="text-xs text-gray-600 mt-2 text-left pl-4">
            Enter a Jersey postcode (e.g., JE2 3NG) to see delivery times
          </p>
        </div>
      )}

      {/* Pickup Mode - Direct to search */}
      {mode === "pickup" && (
        <div className="w-full max-w-3xl mx-auto animate-fade-in-delay-2">
          <Button
            onClick={() => router.push('/search?mode=pickup')}
            className="w-full bg-emerald-600 hover:bg-emerald-700 text-white text-lg py-4 rounded-lg font-medium"
          >
            <MapPin className="h-5 w-5 mr-2" />
            Find Businesses for Pickup
          </Button>
          <p className="text-xs text-gray-600 mt-2 text-center">
            Browse all businesses available for pickup
          </p>
        </div>
      )}

      {/* Error Dialog - Only show for delivery mode */}
      {mode === "delivery" && (
        <Dialog open={errorDialogOpen} onOpenChange={setErrorDialogOpen}>
          <DialogContent className="max-w-[95vw] sm:max-w-md p-4 sm:p-6">
            <DialogTitle className="sr-only">Invalid Postcode</DialogTitle>
            <div className="absolute right-4 top-4">
              <button
                onClick={() => setErrorDialogOpen(false)}
                className="rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
              >
                <X className="h-4 w-4" />
                <span className="sr-only">Close</span>
              </button>
            </div>
            <div className="flex flex-col gap-4">
              <div className="flex items-start gap-3">
                <AlertTriangle className="h-5 w-5 text-red-600 mt-0.5" />
                <div>
                  <h3 className="text-lg font-semibold text-red-600">Invalid Postcode</h3>
                </div>
              </div>
              <p className="text-gray-600 mt-1 pl-8">
                {validationError}
              </p>
            </div>
            <div className="flex flex-col sm:flex-row gap-2 pt-4 w-full justify-center">
              <Button
                variant="outline"
                onClick={() => setErrorDialogOpen(false)}
                className="w-[90%] mx-auto sm:w-[45%] text-[11px] sm:text-sm px-1 sm:px-2 whitespace-nowrap"
                size="sm"
                style={{ minHeight: "36px" }}
              >
                Enter a different postcode
              </Button>
              <Button
                onClick={handleProceedWithDefault}
                className="w-[90%] mx-auto sm:w-[55%] bg-emerald-600 hover:bg-emerald-700 text-[11px] sm:text-sm px-1 sm:px-2 whitespace-nowrap"
                size="sm"
                style={{ minHeight: "36px" }}
              >
                Continue with default location
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};

export default HomeClient;
