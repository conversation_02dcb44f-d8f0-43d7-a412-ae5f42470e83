/**
 * Business availability checking utilities
 * Used to determine if businesses are open/closed for checkout validation
 */

export interface BusinessAvailabilityCheck {
  businessId: string
  businessName: string
  isAvailable: boolean
  reason?: string
}

/**
 * Check if a business is currently available based on opening hours
 */
export function isBusinessWithinOpeningHours(openingHours: any): boolean {
  if (!openingHours) return true // If no opening hours defined, assume always open
  
  const now = new Date()
  const currentDay = now.getDay() // 0 = Sunday, 1 = Monday, etc.
  const currentTime = now.toTimeString().substring(0, 5) // HH:MM format
  
  // Map day numbers to day names
  const dayNames = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday']
  const fullDayName = dayNames[currentDay]
  
  const daySchedule = openingHours[fullDayName]
  
  if (!daySchedule || daySchedule.closed || !daySchedule.open || !daySchedule.close) {
    return false
  }
  
  return currentTime >= daySchedule.open && currentTime <= daySchedule.close
}

/**
 * Check business availability status
 */
export function checkBusinessAvailability(
  businessId: string,
  businessName: string,
  isTemporarilyClosed?: boolean,
  closureMessage?: string,
  openingHours?: any
): BusinessAvailabilityCheck {
  // Check if business is temporarily closed
  if (isTemporarilyClosed) {
    return {
      businessId,
      businessName,
      isAvailable: false,
      reason: closureMessage || "Business is temporarily closed"
    }
  }
  
  // Check opening hours
  if (!isBusinessWithinOpeningHours(openingHours)) {
    return {
      businessId,
      businessName,
      isAvailable: false,
      reason: "Business is currently closed"
    }
  }
  
  return {
    businessId,
    businessName,
    isAvailable: true
  }
}

/**
 * Fetch business availability data from API
 */
export async function fetchBusinessAvailability(businessIds: string[]): Promise<Record<string, BusinessAvailabilityCheck>> {
  try {
    const response = await fetch('/api/businesses/availability', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ businessIds })
    })
    
    if (!response.ok) {
      throw new Error('Failed to fetch business availability')
    }
    
    const data = await response.json()
    return data.businesses || {}
  } catch (error) {
    console.error('Error fetching business availability:', error)
    // Return empty object on error - assume all businesses are available
    return {}
  }
}
