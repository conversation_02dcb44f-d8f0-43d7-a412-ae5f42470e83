"use client"

import { useState, useEffect } from "react"
import { useBusinessData } from "@/hooks/use-business-data"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog"
import { Truck, Clock, MapPin, DollarSign, Settings, AlertCircle, HelpCircle } from "lucide-react"

interface DeliveryConfig {
  pickup_enabled: boolean
  pickup_available: boolean
  delivery_enabled: boolean
  delivery_available: boolean
  use_loop_delivery: boolean
  restriction_logic: string
  delivery_radius: number
  pickup_asap_available: boolean
  pickup_scheduled_available: boolean
  delivery_asap_available: boolean
  delivery_scheduled_available: boolean
  min_advance_booking_minutes: number
  max_advance_booking_days: number
  minimum_order_value: number
  maximum_order_value: number
}

export default function DeliverySettingsPage() {
  const { business, isLoading, error } = useBusinessData()
  const [deliveryConfig, setDeliveryConfig] = useState<DeliveryConfig | null>(null)
  const [isSaving, setIsSaving] = useState(false)
  const [saveMessage, setSaveMessage] = useState<string | null>(null)
  const [showHelpDialog, setShowHelpDialog] = useState(false)

  useEffect(() => {
    if (business) {
      setDeliveryConfig({
        pickup_enabled: business.pickup_enabled ?? true,
        pickup_available: business.pickup_available ?? true,
        delivery_enabled: business.delivery_enabled ?? false,
        delivery_available: business.delivery_available ?? false,
        use_loop_delivery: business.use_loop_delivery ?? true,
        restriction_logic: business.restriction_logic ?? 'either',
        delivery_radius: business.delivery_radius ?? 10,
        pickup_asap_available: business.pickup_asap_available ?? true,
        pickup_scheduled_available: business.pickup_scheduled_available ?? false,
        delivery_asap_available: business.delivery_asap_available ?? false,
        delivery_scheduled_available: business.delivery_scheduled_available ?? false,
        min_advance_booking_minutes: business.min_advance_booking_minutes ?? 30,
        max_advance_booking_days: business.max_advance_booking_days ?? 7,
        minimum_order_value: business.minimum_order_value ?? 15.00,
        maximum_order_value: business.maximum_order_value ?? 200.00
      })
    }
  }, [business])

  const handleSave = async () => {
    if (!deliveryConfig) return

    setIsSaving(true)
    setSaveMessage(null)

    try {
      const token = localStorage.getItem('loop_jersey_auth_token') || ''
      const response = await fetch('/api/business-admin/delivery-settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : '',
        },
        body: JSON.stringify(deliveryConfig)
      })

      if (!response.ok) {
        throw new Error(`Failed to save: ${response.status}`)
      }

      setSaveMessage('Delivery settings saved successfully!')
      setTimeout(() => setSaveMessage(null), 3000)
    } catch (err) {
      console.error('Error saving delivery settings:', err)
      setSaveMessage('Failed to save delivery settings. Please try again.')
      setTimeout(() => setSaveMessage(null), 5000)
    } finally {
      setIsSaving(false)
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-emerald-600"></div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Error Loading Settings</h2>
          <p className="text-gray-600">{error}</p>
        </div>
      </div>
    )
  }

  if (!deliveryConfig) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-emerald-600"></div>
      </div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">Delivery Settings</h1>
            <p className="text-gray-600">Configure your delivery options and pricing</p>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowHelpDialog(true)}
            className="h-8 flex items-center gap-2 admin-button"
          >
            <HelpCircle className="h-3.5 w-3.5" />
            Help
          </Button>
        </div>
      </div>

      {saveMessage && (
        <div className={`mb-6 p-4 rounded-md ${
          saveMessage.includes('successfully') 
            ? 'bg-green-50 text-green-800 border border-green-200' 
            : 'bg-red-50 text-red-800 border border-red-200'
        }`}>
          {saveMessage}
        </div>
      )}

      <div className="space-y-8">
        {/* Service Options */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center mb-4">
            <Settings className="w-5 h-5 text-emerald-600 mr-2" />
            <h2 className="text-lg font-semibold text-gray-900">Service Options</h2>
          </div>
          <p className="text-gray-600 mb-6">Choose which services your business offers to customers</p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Pickup Service */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium text-gray-900">Pickup Service</h3>
                  <p className="text-sm text-gray-600">Customers collect orders</p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={deliveryConfig.pickup_enabled}
                    onChange={(e) => setDeliveryConfig({
                      ...deliveryConfig,
                      pickup_enabled: e.target.checked
                    })}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-emerald-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-emerald-600"></div>
                </label>
              </div>

              {deliveryConfig.pickup_enabled && (
                <div className="ml-4 space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-700">Currently Available</span>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={deliveryConfig.pickup_available}
                        onChange={(e) => setDeliveryConfig({
                          ...deliveryConfig,
                          pickup_available: e.target.checked
                        })}
                        className="sr-only peer"
                      />
                      <div className="w-9 h-5 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-emerald-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-emerald-600"></div>
                    </label>
                  </div>
                </div>
              )}
            </div>

            {/* Delivery Service */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium text-gray-900">Delivery Service</h3>
                  <p className="text-sm text-gray-600">Deliver orders to customers</p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={deliveryConfig.delivery_enabled}
                    onChange={(e) => setDeliveryConfig({
                      ...deliveryConfig,
                      delivery_enabled: e.target.checked
                    })}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-emerald-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-emerald-600"></div>
                </label>
              </div>

              {deliveryConfig.delivery_enabled && (
                <div className="ml-4 space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-700">Currently Available</span>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={deliveryConfig.delivery_available}
                        onChange={(e) => setDeliveryConfig({
                          ...deliveryConfig,
                          delivery_available: e.target.checked
                        })}
                        className="sr-only peer"
                      />
                      <div className="w-9 h-5 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-emerald-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-emerald-600"></div>
                    </label>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Delivery Service Provider */}
        {deliveryConfig.delivery_enabled && (
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center mb-4">
              <Truck className="w-5 h-5 text-emerald-600 mr-2" />
              <h2 className="text-lg font-semibold text-gray-900">Delivery Service Provider</h2>
            </div>
            <p className="text-gray-600 mb-6">Choose who handles your deliveries</p>

            <div className="space-y-4">
              <label className="flex items-center p-4 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50">
                <input
                  type="radio"
                  name="delivery_provider"
                  checked={deliveryConfig.use_loop_delivery}
                  onChange={() => setDeliveryConfig({
                    ...deliveryConfig,
                    use_loop_delivery: true
                  })}
                  className="w-4 h-4 text-emerald-600 border-gray-300 focus:ring-emerald-500"
                />
                <div className="ml-3">
                  <div className="font-medium text-gray-900">Use Loop Delivery Service</div>
                  <div className="text-sm text-gray-600">Let Loop handle your deliveries with our driver network</div>
                </div>
              </label>

              <label className="flex items-center p-4 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50">
                <input
                  type="radio"
                  name="delivery_provider"
                  checked={!deliveryConfig.use_loop_delivery}
                  onChange={() => setDeliveryConfig({
                    ...deliveryConfig,
                    use_loop_delivery: false
                  })}
                  className="w-4 h-4 text-emerald-600 border-gray-300 focus:ring-emerald-500"
                />
                <div className="ml-3">
                  <div className="font-medium text-gray-900">Use Own Delivery Service</div>
                  <div className="text-sm text-gray-600">Handle deliveries with your own staff and vehicles</div>
                </div>
              </label>
            </div>
          </div>
        )}

        {/* Save Button */}
        <div className="flex justify-end">
          <button
            onClick={handleSave}
            disabled={isSaving}
            className="px-6 py-2 bg-emerald-600 text-white rounded-md hover:bg-emerald-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
          >
            {isSaving ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Saving...
              </>
            ) : (
              'Save Settings'
            )}
          </button>
        </div>
      </div>

      {/* Help Dialog */}
      <Dialog open={showHelpDialog} onOpenChange={setShowHelpDialog}>
        <DialogContent className="sm:max-w-[700px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <HelpCircle className="h-5 w-5 text-blue-600" />
              Delivery Settings Help
            </DialogTitle>
            <DialogDescription>
              Learn how to configure your delivery and pickup options effectively
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-6 max-h-[60vh] overflow-y-auto">
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-3">Service Options</h3>
                <div className="space-y-3 text-sm text-gray-600">
                  <div>
                    <strong className="text-gray-900">Pickup Service:</strong> Allows customers to collect orders from your business location. This is always recommended as it gives customers flexibility and reduces your delivery costs.
                  </div>
                  <div>
                    <strong className="text-gray-900">Delivery Service:</strong> Enables order delivery to customer locations. You can choose between Loop's delivery service or handle deliveries yourself.
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-3">Permanent vs Temporary Settings</h3>
                <div className="space-y-3 text-sm text-gray-600">
                  <div>
                    <strong className="text-gray-900">Service Enabled:</strong> This is your permanent configuration - whether your business has the capability to offer pickup or delivery.
                  </div>
                  <div>
                    <strong className="text-gray-900">Currently Available:</strong> This is a temporary toggle - use this when you need to temporarily disable a service (e.g., when delivery drivers are sick) without losing your configuration settings.
                  </div>
                  <div className="bg-blue-50 p-3 rounded-md">
                    <strong className="text-blue-900">Tip:</strong> A service is only available to customers when both "Enabled" and "Currently Available" are turned on.
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-3">Delivery Service Provider</h3>
                <div className="space-y-3 text-sm text-gray-600">
                  <div>
                    <strong className="text-gray-900">Loop Delivery Service:</strong> Loop handles all deliveries using our driver network. You don't need to worry about drivers, vehicles, or delivery logistics.
                  </div>
                  <div>
                    <strong className="text-gray-900">Own Delivery Service:</strong> You handle deliveries with your own staff and vehicles. This gives you more control but requires managing drivers and delivery operations.
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-3">Switching Between Services</h3>
                <div className="space-y-3 text-sm text-gray-600">
                  <div>
                    <strong className="text-gray-900">Seamless Transitions:</strong> You can switch between Loop delivery and your own delivery service at any time. Orders will be seamlessly redirected to the selected delivery channel.
                  </div>
                  <div>
                    <strong className="text-gray-900">Pickup & Delivery Changes:</strong> Similarly, enabling or disabling pickup and delivery services is seamless and takes effect immediately for new orders.
                  </div>
                  <div className="bg-amber-50 p-3 rounded-md border border-amber-200">
                    <strong className="text-amber-900">Important:</strong> We don't recommend switching delivery services frequently unless necessary. Before clicking save, make sure you have configured all relevant settings for your chosen delivery method to avoid service disruptions.
                  </div>
                  <div className="bg-blue-50 p-3 rounded-md">
                    <strong className="text-blue-900">Best Practice:</strong> Test your delivery settings during quiet periods to ensure everything works as expected before busy service times.
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-3">Coming Soon</h3>
                <div className="text-sm text-gray-600">
                  <p>Additional settings will be added to this page including:</p>
                  <ul className="list-disc list-inside mt-2 space-y-1">
                    <li>Geographic delivery restrictions (parish-based)</li>
                    <li>Delivery pricing configuration</li>
                    <li>Timing options (ASAP vs scheduled orders)</li>
                    <li>Order value limits and minimums</li>
                  </ul>
                </div>
              </div>
          </div>
          <DialogFooter>
            <Button onClick={() => setShowHelpDialog(false)}>
              Got it, thanks!
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
