"use client"

import { useState, useEffect } from "react"
import { useBusinessData } from "@/hooks/use-business-data"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog"
import { Truck, Clock, MapPin, DollarSign, Settings, AlertCircle, HelpCircle, ChevronDown, ChevronUp } from "lucide-react"

interface DeliveryConfig {
  pickup_enabled: boolean
  pickup_available: boolean
  delivery_enabled: boolean
  delivery_available: boolean
  use_loop_delivery: boolean
  restriction_logic: string
  delivery_radius: number
  pickup_asap_available: boolean
  pickup_scheduled_available: boolean
  delivery_asap_available: boolean
  delivery_scheduled_available: boolean
  min_advance_booking_minutes: number
  max_advance_booking_days: number
  minimum_order_value: number
  maximum_order_value: number
}

export default function DeliverySettingsPage() {
  const { business, isLoading, error } = useBusinessData()
  const [deliveryConfig, setDeliveryConfig] = useState<DeliveryConfig | null>(null)
  const [isSaving, setIsSaving] = useState(false)
  const [saveMessage, setSaveMessage] = useState<string | null>(null)
  const [showHelpDialog, setShowHelpDialog] = useState(false)
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({
    'service-options': true, // Start with first section expanded
    'permanent-vs-temporary': false,
    'delivery-provider': false,
    'switching-services': false,
    'coming-soon': false
  })

  const toggleSection = (sectionId: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [sectionId]: !prev[sectionId]
    }))
  }

  useEffect(() => {
    if (business) {
      setDeliveryConfig({
        pickup_enabled: business.pickup_enabled ?? true,
        pickup_available: business.pickup_available ?? true,
        delivery_enabled: business.delivery_enabled ?? false,
        delivery_available: business.delivery_available ?? false,
        use_loop_delivery: business.use_loop_delivery ?? true,
        restriction_logic: business.restriction_logic ?? 'either',
        delivery_radius: business.delivery_radius ?? 10,
        pickup_asap_available: business.pickup_asap_available ?? true,
        pickup_scheduled_available: business.pickup_scheduled_available ?? false,
        delivery_asap_available: business.delivery_asap_available ?? false,
        delivery_scheduled_available: business.delivery_scheduled_available ?? false,
        min_advance_booking_minutes: business.min_advance_booking_minutes ?? 30,
        max_advance_booking_days: business.max_advance_booking_days ?? 7,
        minimum_order_value: business.minimum_order_value ?? 15.00,
        maximum_order_value: business.maximum_order_value ?? 200.00
      })
    }
  }, [business])

  const handleSave = async () => {
    if (!deliveryConfig) return

    setIsSaving(true)
    setSaveMessage(null)

    try {
      const token = localStorage.getItem('loop_jersey_auth_token') || ''
      const response = await fetch('/api/business-admin/delivery-settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : '',
        },
        body: JSON.stringify(deliveryConfig)
      })

      if (!response.ok) {
        throw new Error(`Failed to save: ${response.status}`)
      }

      setSaveMessage('Delivery settings saved successfully!')
      setTimeout(() => setSaveMessage(null), 3000)
    } catch (err) {
      console.error('Error saving delivery settings:', err)
      setSaveMessage('Failed to save delivery settings. Please try again.')
      setTimeout(() => setSaveMessage(null), 5000)
    } finally {
      setIsSaving(false)
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-emerald-600"></div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Error Loading Settings</h2>
          <p className="text-gray-600">{error}</p>
        </div>
      </div>
    )
  }

  if (!deliveryConfig) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-emerald-600"></div>
      </div>
    )
  }

  return (
    <div className="max-w-5xl mx-auto p-6 space-y-8">
      {/* Header Section */}
      <div className="bg-gradient-to-r from-emerald-50 to-blue-50 rounded-xl p-6 border border-emerald-100">
        <div className="flex items-center justify-between">
          <div>
            <div className="flex items-center gap-3 mb-3">
              <div className="p-2 bg-emerald-100 rounded-lg">
                <Truck className="h-6 w-6 text-emerald-600" />
              </div>
              <h1 className="text-3xl font-bold text-gray-900">Delivery Settings</h1>
            </div>
            <p className="text-gray-600 text-lg">Configure your delivery options and pricing to serve customers better</p>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowHelpDialog(true)}
            className="h-9 flex items-center gap-2 bg-white/80 backdrop-blur-sm border-emerald-200 hover:bg-white hover:border-emerald-300 transition-all duration-200"
          >
            <HelpCircle className="h-4 w-4" />
            Help
          </Button>
        </div>
      </div>

      {/* Status Message */}
      {saveMessage && (
        <div className={`p-4 rounded-xl border-l-4 shadow-sm ${
          saveMessage.includes('successfully')
            ? 'bg-green-50 text-green-800 border-l-green-400 border border-green-200'
            : 'bg-red-50 text-red-800 border-l-red-400 border border-red-200'
        }`}>
          <div className="flex items-center">
            <div className={`p-1 rounded-full mr-3 ${
              saveMessage.includes('successfully') ? 'bg-green-100' : 'bg-red-100'
            }`}>
              {saveMessage.includes('successfully') ? '✓' : '⚠'}
            </div>
            {saveMessage}
          </div>
        </div>
      )}

      <div className="space-y-6">
        {/* Service Options */}
        <div className="bg-white rounded-xl border border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200">
          <div className="p-6 border-b border-gray-100">
            <div className="flex items-center mb-2">
              <div className="p-2 bg-emerald-100 rounded-lg mr-3">
                <Settings className="w-5 h-5 text-emerald-600" />
              </div>
              <h2 className="text-xl font-semibold text-gray-900">Service Options</h2>
            </div>
            <p className="text-gray-600 ml-12">Choose which services your business offers to customers</p>
          </div>
          <div className="p-6">

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Pickup Service */}
              <div className="bg-gray-50 rounded-lg p-5 border border-gray-200 hover:border-emerald-200 transition-colors duration-200">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center">
                    <div className="p-2 bg-blue-100 rounded-lg mr-3">
                      <MapPin className="w-4 h-4 text-blue-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">Pickup Service</h3>
                      <p className="text-sm text-gray-600">Customers collect orders</p>
                    </div>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={deliveryConfig.pickup_enabled}
                      onChange={(e) => setDeliveryConfig({
                        ...deliveryConfig,
                        pickup_enabled: e.target.checked
                      })}
                      className="sr-only peer"
                    />
                    <div className="w-12 h-7 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-emerald-300/50 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[3px] after:left-[3px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all after:shadow-sm peer-checked:bg-gradient-to-r peer-checked:from-emerald-500 peer-checked:to-emerald-600"></div>
                  </label>
                </div>
              </div>

                {deliveryConfig.pickup_enabled && (
                  <div className="mt-4 p-3 bg-white rounded-lg border border-gray-200">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="w-2 h-2 bg-blue-400 rounded-full mr-2"></div>
                        <span className="text-sm font-medium text-gray-700">Currently Available</span>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          checked={deliveryConfig.pickup_available}
                          onChange={(e) => setDeliveryConfig({
                            ...deliveryConfig,
                            pickup_available: e.target.checked
                          })}
                          className="sr-only peer"
                        />
                        <div className="w-10 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300/50 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all after:shadow-sm peer-checked:bg-gradient-to-r peer-checked:from-blue-500 peer-checked:to-blue-600"></div>
                      </label>
                    </div>
                  </div>
                )}
            </div>

              {/* Delivery Service */}
              <div className="bg-gray-50 rounded-lg p-5 border border-gray-200 hover:border-emerald-200 transition-colors duration-200">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center">
                    <div className="p-2 bg-emerald-100 rounded-lg mr-3">
                      <Truck className="w-4 h-4 text-emerald-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">Delivery Service</h3>
                      <p className="text-sm text-gray-600">Deliver orders to customers</p>
                    </div>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={deliveryConfig.delivery_enabled}
                      onChange={(e) => setDeliveryConfig({
                        ...deliveryConfig,
                        delivery_enabled: e.target.checked
                      })}
                      className="sr-only peer"
                    />
                    <div className="w-12 h-7 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-emerald-300/50 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[3px] after:left-[3px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all after:shadow-sm peer-checked:bg-gradient-to-r peer-checked:from-emerald-500 peer-checked:to-emerald-600"></div>
                  </label>
                </div>
              </div>

                {deliveryConfig.delivery_enabled && (
                  <div className="mt-4 p-3 bg-white rounded-lg border border-gray-200">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="w-2 h-2 bg-emerald-400 rounded-full mr-2"></div>
                        <span className="text-sm font-medium text-gray-700">Currently Available</span>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          checked={deliveryConfig.delivery_available}
                          onChange={(e) => setDeliveryConfig({
                            ...deliveryConfig,
                            delivery_available: e.target.checked
                          })}
                          className="sr-only peer"
                        />
                        <div className="w-10 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-emerald-300/50 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all after:shadow-sm peer-checked:bg-gradient-to-r peer-checked:from-emerald-500 peer-checked:to-emerald-600"></div>
                      </label>
                    </div>
                  </div>
                )}
            </div>
          </div>
        </div>

        {/* Delivery Service Provider */}
        {deliveryConfig.delivery_enabled && (
          <div className="bg-white rounded-xl border border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200">
            <div className="p-6 border-b border-gray-100">
              <div className="flex items-center mb-2">
                <div className="p-2 bg-blue-100 rounded-lg mr-3">
                  <Truck className="w-5 h-5 text-blue-600" />
                </div>
                <h2 className="text-xl font-semibold text-gray-900">Delivery Service Provider</h2>
              </div>
              <p className="text-gray-600 ml-12">Choose who handles your deliveries</p>
            </div>
            <div className="p-6">

              <div className="space-y-4">
                <label className="flex items-center p-5 border-2 border-gray-200 rounded-xl cursor-pointer hover:border-emerald-300 hover:bg-emerald-50/50 transition-all duration-200 group">
                  <input
                    type="radio"
                    name="delivery_provider"
                    checked={deliveryConfig.use_loop_delivery}
                    onChange={() => setDeliveryConfig({
                      ...deliveryConfig,
                      use_loop_delivery: true
                    })}
                    className="w-5 h-5 text-emerald-600 border-gray-300 focus:ring-emerald-500 focus:ring-2"
                  />
                  <div className="ml-4 flex items-center">
                    <div className="p-2 bg-emerald-100 rounded-lg mr-3 group-hover:bg-emerald-200 transition-colors">
                      <Truck className="w-4 h-4 text-emerald-600" />
                    </div>
                    <div>
                      <div className="font-semibold text-gray-900">Use Loop Delivery Service</div>
                      <div className="text-sm text-gray-600">Let Loop handle your deliveries with our driver network</div>
                    </div>
                  </div>
                </label>

                <label className="flex items-center p-5 border-2 border-gray-200 rounded-xl cursor-pointer hover:border-blue-300 hover:bg-blue-50/50 transition-all duration-200 group">
                  <input
                    type="radio"
                    name="delivery_provider"
                    checked={!deliveryConfig.use_loop_delivery}
                    onChange={() => setDeliveryConfig({
                      ...deliveryConfig,
                      use_loop_delivery: false
                    })}
                    className="w-5 h-5 text-blue-600 border-gray-300 focus:ring-blue-500 focus:ring-2"
                  />
                  <div className="ml-4 flex items-center">
                    <div className="p-2 bg-blue-100 rounded-lg mr-3 group-hover:bg-blue-200 transition-colors">
                      <Settings className="w-4 h-4 text-blue-600" />
                    </div>
                    <div>
                      <div className="font-semibold text-gray-900">Use Own Delivery Service</div>
                      <div className="text-sm text-gray-600">Handle deliveries with your own staff and vehicles</div>
                    </div>
                  </div>
                </label>
            </div>
          </div>
        )}

        {/* Save Button */}
        <div className="flex justify-end pt-4">
          <button
            onClick={handleSave}
            disabled={isSaving}
            className="px-8 py-3 bg-gradient-to-r from-emerald-600 to-emerald-700 text-white font-semibold rounded-xl hover:from-emerald-700 hover:to-emerald-800 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 flex items-center gap-2"
          >
            {isSaving ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                Saving...
              </>
            ) : (
              <>
                <Settings className="w-4 h-4" />
                Save Settings
              </>
            )}
          </button>
        </div>
      </div>

      {/* Help Dialog */}
      <Dialog open={showHelpDialog} onOpenChange={setShowHelpDialog}>
        <DialogContent className="sm:max-w-[700px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <HelpCircle className="h-5 w-5 text-blue-600" />
              Delivery Settings Help
            </DialogTitle>
            <DialogDescription>
              Learn how to configure your delivery and pickup options effectively
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 max-h-[60vh] overflow-y-auto">
            {/* Service Options Section */}
            <div className="border border-gray-200 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-200">
              <button
                onClick={() => toggleSection('service-options')}
                className="w-full flex items-center justify-between p-5 text-left hover:bg-gradient-to-r hover:from-emerald-50 hover:to-blue-50 transition-all duration-200 rounded-t-xl"
              >
                <div className="flex items-center">
                  <div className="p-2 bg-emerald-100 rounded-lg mr-3">
                    <Settings className="h-4 w-4 text-emerald-600" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900">Service Options</h3>
                </div>
                {expandedSections['service-options'] ? (
                  <ChevronUp className="h-5 w-5 text-gray-500 transition-transform duration-200" />
                ) : (
                  <ChevronDown className="h-5 w-5 text-gray-500 transition-transform duration-200" />
                )}
              </button>
              {expandedSections['service-options'] && (
                <div className="px-5 pb-5 space-y-4 text-sm text-gray-600 border-t border-gray-100 bg-gray-50/50">
                  <div className="p-3 bg-white rounded-lg border border-gray-200">
                    <strong className="text-gray-900">Pickup Service:</strong> Allows customers to collect orders from your business location. This is always recommended as it gives customers flexibility and reduces your delivery costs.
                  </div>
                  <div className="p-3 bg-white rounded-lg border border-gray-200">
                    <strong className="text-gray-900">Delivery Service:</strong> Enables order delivery to customer locations. You can choose between Loop's delivery service or handle deliveries yourself.
                  </div>
                </div>
              )}
            </div>

            {/* Permanent vs Temporary Settings Section */}
            <div className="border border-gray-200 rounded-lg">
              <button
                onClick={() => toggleSection('permanent-vs-temporary')}
                className="w-full flex items-center justify-between p-4 text-left hover:bg-gray-50 transition-colors"
              >
                <h3 className="text-lg font-medium text-gray-900">Permanent vs Temporary Settings</h3>
                {expandedSections['permanent-vs-temporary'] ? (
                  <ChevronUp className="h-5 w-5 text-gray-500" />
                ) : (
                  <ChevronDown className="h-5 w-5 text-gray-500" />
                )}
              </button>
              {expandedSections['permanent-vs-temporary'] && (
                <div className="px-4 pb-4 space-y-3 text-sm text-gray-600 border-t border-gray-100">
                  <div>
                    <strong className="text-gray-900">Service Enabled:</strong> This is your permanent configuration - whether your business has the capability to offer pickup or delivery.
                  </div>
                  <div>
                    <strong className="text-gray-900">Currently Available:</strong> This is a temporary toggle - use this when you need to temporarily disable a service (e.g., when delivery drivers are sick) without losing your configuration settings.
                  </div>
                  <div className="bg-blue-50 p-3 rounded-md">
                    <strong className="text-blue-900">Tip:</strong> A service is only available to customers when both "Enabled" and "Currently Available" are turned on.
                  </div>
                </div>
              )}
            </div>

            {/* Delivery Service Provider Section */}
            <div className="border border-gray-200 rounded-lg">
              <button
                onClick={() => toggleSection('delivery-provider')}
                className="w-full flex items-center justify-between p-4 text-left hover:bg-gray-50 transition-colors"
              >
                <h3 className="text-lg font-medium text-gray-900">Delivery Service Provider</h3>
                {expandedSections['delivery-provider'] ? (
                  <ChevronUp className="h-5 w-5 text-gray-500" />
                ) : (
                  <ChevronDown className="h-5 w-5 text-gray-500" />
                )}
              </button>
              {expandedSections['delivery-provider'] && (
                <div className="px-4 pb-4 space-y-3 text-sm text-gray-600 border-t border-gray-100">
                  <div>
                    <strong className="text-gray-900">Loop Delivery Service:</strong> Loop handles all deliveries using our driver network. You don't need to worry about drivers, vehicles, or delivery logistics.
                  </div>
                  <div>
                    <strong className="text-gray-900">Own Delivery Service:</strong> You handle deliveries with your own staff and vehicles. This gives you more control but requires managing drivers and delivery operations.
                  </div>
                </div>
              )}
            </div>

            {/* Switching Between Services Section */}
            <div className="border border-gray-200 rounded-lg">
              <button
                onClick={() => toggleSection('switching-services')}
                className="w-full flex items-center justify-between p-4 text-left hover:bg-gray-50 transition-colors"
              >
                <h3 className="text-lg font-medium text-gray-900">Switching Between Services</h3>
                {expandedSections['switching-services'] ? (
                  <ChevronUp className="h-5 w-5 text-gray-500" />
                ) : (
                  <ChevronDown className="h-5 w-5 text-gray-500" />
                )}
              </button>
              {expandedSections['switching-services'] && (
                <div className="px-4 pb-4 space-y-3 text-sm text-gray-600 border-t border-gray-100">
                  <div>
                    <strong className="text-gray-900">Seamless Transitions:</strong> You can switch between Loop delivery and your own delivery service at any time. Orders will be seamlessly redirected to the selected delivery channel.
                  </div>
                  <div>
                    <strong className="text-gray-900">Pickup & Delivery Changes:</strong> Similarly, enabling or disabling pickup and delivery services is seamless and takes effect immediately for new orders.
                  </div>
                  <div className="bg-amber-50 p-3 rounded-md border border-amber-200">
                    <strong className="text-amber-900">Important:</strong> We don't recommend switching delivery services frequently unless necessary. Before clicking save, make sure you have configured all relevant settings for your chosen delivery method to avoid service disruptions.
                  </div>
                  <div className="bg-blue-50 p-3 rounded-md">
                    <strong className="text-blue-900">Best Practice:</strong> Test your delivery settings during quiet periods to ensure everything works as expected before busy service times.
                  </div>
                </div>
              )}
            </div>

            {/* Coming Soon Section */}
            <div className="border border-gray-200 rounded-lg">
              <button
                onClick={() => toggleSection('coming-soon')}
                className="w-full flex items-center justify-between p-4 text-left hover:bg-gray-50 transition-colors"
              >
                <h3 className="text-lg font-medium text-gray-900">Coming Soon</h3>
                {expandedSections['coming-soon'] ? (
                  <ChevronUp className="h-5 w-5 text-gray-500" />
                ) : (
                  <ChevronDown className="h-5 w-5 text-gray-500" />
                )}
              </button>
              {expandedSections['coming-soon'] && (
                <div className="px-4 pb-4 text-sm text-gray-600 border-t border-gray-100">
                  <p>Additional settings will be added to this page including:</p>
                  <ul className="list-disc list-inside mt-2 space-y-1">
                    <li>Geographic delivery restrictions (parish-based)</li>
                    <li>Delivery pricing configuration</li>
                    <li>Timing options (ASAP vs scheduled orders)</li>
                    <li>Order value limits and minimums</li>
                  </ul>
                </div>
              )}
            </div>
          </div>
          <DialogFooter>
            <Button onClick={() => setShowHelpDialog(false)}>
              Got it, thanks!
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
