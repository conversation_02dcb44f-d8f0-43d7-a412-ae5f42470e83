"use client"

import { useState, useEffect } from "react"
import { useBusinessData } from "@/hooks/use-business-data"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog"
import { Truck, Clock, MapPin, DollarSign, Settings, AlertCircle, HelpCircle, ChevronDown, ChevronUp } from "lucide-react"

interface DeliveryConfig {
  pickup_enabled: boolean
  pickup_available: boolean
  delivery_enabled: boolean
  delivery_available: boolean
  use_loop_delivery: boolean
  restriction_logic: string
  delivery_radius: number
  pickup_asap_available: boolean
  pickup_scheduled_available: boolean
  delivery_asap_available: boolean
  delivery_scheduled_available: boolean
  min_advance_booking_minutes: number
  max_advance_booking_days: number
  minimum_order_value: number
  maximum_order_value: number
}

export default function DeliverySettingsPage() {
  const { business, isLoading, error } = useBusinessData()
  const [deliveryConfig, setDeliveryConfig] = useState<DeliveryConfig | null>(null)

  // Debug logging
  console.log('DeliverySettingsPage - Debug:', {
    business,
    isLoading,
    error,
    deliveryConfig
  })
  const [isSaving, setIsSaving] = useState(false)
  const [saveMessage, setSaveMessage] = useState<string | null>(null)
  const [showHelpDialog, setShowHelpDialog] = useState(false)
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({
    'service-options': true,
    'permanent-vs-temporary': false,
    'delivery-provider': false,
    'switching-services': false,
    'coming-soon': false
  })

  const toggleSection = (sectionId: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [sectionId]: !prev[sectionId]
    }))
  }

  useEffect(() => {
    if (business) {
      setDeliveryConfig({
        pickup_enabled: business.pickup_enabled || true,
        pickup_available: business.pickup_available !== false,
        delivery_enabled: business.delivery_enabled || false,
        delivery_available: business.delivery_available !== false,
        use_loop_delivery: business.use_loop_delivery || false,
        restriction_logic: business.restriction_logic || 'EITHER',
        delivery_radius: business.delivery_radius || 5,
        pickup_asap_available: business.pickup_asap_available !== false,
        pickup_scheduled_available: business.pickup_scheduled_available !== false,
        delivery_asap_available: business.delivery_asap_available !== false,
        delivery_scheduled_available: business.delivery_scheduled_available !== false,
        min_advance_booking_minutes: business.min_advance_booking_minutes || 30,
        max_advance_booking_days: business.max_advance_booking_days || 7,
        minimum_order_value: business.minimum_order_value || 0,
        maximum_order_value: business.maximum_order_value || 1000
      })
    }
  }, [business])

  const handleSave = async () => {
    if (!deliveryConfig) return

    setIsSaving(true)
    setSaveMessage(null)

    try {
      const response = await fetch('/api/business-admin/delivery-settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(deliveryConfig),
      })

      if (!response.ok) {
        throw new Error('Failed to save settings')
      }

      setSaveMessage('Settings saved successfully!')
      setTimeout(() => setSaveMessage(null), 3000)
    } catch (error) {
      console.error('Error saving delivery settings:', error)
      setSaveMessage('Error saving settings. Please try again.')
      setTimeout(() => setSaveMessage(null), 5000)
    } finally {
      setIsSaving(false)
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-emerald-600"></div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Error Loading Settings</h2>
          <p className="text-gray-600">{error}</p>
        </div>
      </div>
    )
  }

  if (!deliveryConfig) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-emerald-600"></div>
      </div>
    )
  }

  return (
    <>
      <div className="max-w-5xl mx-auto p-6 space-y-8">
        {/* Header Section */}
        <div className="bg-gradient-to-r from-emerald-50 to-blue-50 rounded-xl p-6 border border-emerald-100">
          <div className="flex items-center justify-between">
            <div>
              <div className="flex items-center gap-3 mb-3">
                <div className="p-2 bg-emerald-100 rounded-lg">
                  <Truck className="h-6 w-6 text-emerald-600" />
                </div>
                <h1 className="text-3xl font-bold text-gray-900">Delivery Settings</h1>
              </div>
              <p className="text-gray-600 text-lg">Configure your pickup and delivery options</p>
            </div>
            <Button
              onClick={() => setShowHelpDialog(true)}
              variant="outline"
              className="flex items-center gap-2 bg-white hover:bg-gray-50 border-2 border-gray-200 hover:border-blue-300 transition-all duration-200"
            >
              <HelpCircle className="h-4 w-4" />
              Help
            </Button>
          </div>
        </div>

        {/* Status Message */}
        {saveMessage && (
          <div className={`p-4 rounded-xl border-l-4 flex items-center gap-3 ${
            saveMessage.includes('Error') 
              ? 'bg-red-50 border-red-400 text-red-700' 
              : 'bg-emerald-50 border-emerald-400 text-emerald-700'
          }`}>
            <AlertCircle className="h-5 w-5" />
            {saveMessage}
          </div>
        )}

        {/* Service Options */}
        <div className="grid gap-8 lg:grid-cols-2">
          {/* Pickup Service */}
          <div className="bg-white rounded-xl border border-gray-200 p-6 shadow-sm hover:shadow-md transition-shadow duration-200">
            <div className="flex items-center gap-3 mb-6">
              <div className="p-2 bg-blue-100 rounded-lg">
                <MapPin className="h-5 w-5 text-blue-600" />
              </div>
              <h2 className="text-xl font-semibold text-gray-900">Pickup Service</h2>
            </div>
            
            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div>
                  <div className="font-medium text-gray-900">Enable Pickup</div>
                  <div className="text-sm text-gray-600">Allow customers to collect orders</div>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={deliveryConfig.pickup_enabled}
                    onChange={(e) => setDeliveryConfig({
                      ...deliveryConfig,
                      pickup_enabled: e.target.checked
                    })}
                    className="sr-only peer"
                  />
                  <div className="w-14 h-7 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:left-[4px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-6 after:w-6 after:transition-all peer-checked:bg-gradient-to-r peer-checked:from-blue-500 peer-checked:to-blue-600 shadow-lg"></div>
                </label>
              </div>

              {deliveryConfig.pickup_enabled && (
                <div className="ml-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium text-gray-900">Temporarily Available</div>
                      <div className="text-sm text-gray-600">Can be toggled for operational reasons</div>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={deliveryConfig.pickup_available}
                        onChange={(e) => setDeliveryConfig({
                          ...deliveryConfig,
                          pickup_available: e.target.checked
                        })}
                        className="sr-only peer"
                      />
                      <div className="w-12 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-gradient-to-r peer-checked:from-blue-400 peer-checked:to-blue-500"></div>
                    </label>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Delivery Service */}
          <div className="bg-white rounded-xl border border-gray-200 p-6 shadow-sm hover:shadow-md transition-shadow duration-200">
            <div className="flex items-center gap-3 mb-6">
              <div className="p-2 bg-emerald-100 rounded-lg">
                <Truck className="h-5 w-5 text-emerald-600" />
              </div>
              <h2 className="text-xl font-semibold text-gray-900">Delivery Service</h2>
            </div>
            
            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div>
                  <div className="font-medium text-gray-900">Enable Delivery</div>
                  <div className="text-sm text-gray-600">Allow order delivery to customers</div>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={deliveryConfig.delivery_enabled}
                    onChange={(e) => setDeliveryConfig({
                      ...deliveryConfig,
                      delivery_enabled: e.target.checked
                    })}
                    className="sr-only peer"
                  />
                  <div className="w-14 h-7 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-emerald-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:left-[4px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-6 after:w-6 after:transition-all peer-checked:bg-gradient-to-r peer-checked:from-emerald-500 peer-checked:to-emerald-600 shadow-lg"></div>
                </label>
              </div>

              {deliveryConfig.delivery_enabled && (
                <div className="ml-4 p-4 bg-emerald-50 rounded-lg border border-emerald-200">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium text-gray-900">Temporarily Available</div>
                      <div className="text-sm text-gray-600">Can be toggled for operational reasons</div>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={deliveryConfig.delivery_available}
                        onChange={(e) => setDeliveryConfig({
                          ...deliveryConfig,
                          delivery_available: e.target.checked
                        })}
                        className="sr-only peer"
                      />
                      <div className="w-12 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-emerald-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-gradient-to-r peer-checked:from-emerald-400 peer-checked:to-emerald-500"></div>
                    </label>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Delivery Provider Selection */}
        {deliveryConfig.delivery_enabled && (
          <div className="bg-white rounded-xl border border-gray-200 p-6 shadow-sm">
            <div className="flex items-center gap-3 mb-6">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Settings className="h-5 w-5 text-purple-600" />
              </div>
              <h2 className="text-xl font-semibold text-gray-900">Delivery Service Provider</h2>
            </div>
            
            <div className="space-y-4">
              <label className="flex items-center p-5 border-2 border-gray-200 rounded-xl cursor-pointer hover:border-emerald-300 hover:bg-emerald-50/50 transition-all duration-200 group">
                <input
                  type="radio"
                  name="delivery_provider"
                  checked={deliveryConfig.use_loop_delivery}
                  onChange={() => setDeliveryConfig({
                    ...deliveryConfig,
                    use_loop_delivery: true
                  })}
                  className="w-5 h-5 text-emerald-600 border-gray-300 focus:ring-emerald-500 focus:ring-2"
                />
                <div className="ml-4 flex items-center">
                  <div className="p-2 bg-emerald-100 rounded-lg mr-3 group-hover:bg-emerald-200 transition-colors">
                    <Truck className="w-4 h-4 text-emerald-600" />
                  </div>
                  <div>
                    <div className="font-semibold text-gray-900">Use Loop Delivery Service</div>
                    <div className="text-sm text-gray-600">Let Loop handle your deliveries with our driver network</div>
                  </div>
                </div>
              </label>

              <label className="flex items-center p-5 border-2 border-gray-200 rounded-xl cursor-pointer hover:border-blue-300 hover:bg-blue-50/50 transition-all duration-200 group">
                <input
                  type="radio"
                  name="delivery_provider"
                  checked={!deliveryConfig.use_loop_delivery}
                  onChange={() => setDeliveryConfig({
                    ...deliveryConfig,
                    use_loop_delivery: false
                  })}
                  className="w-5 h-5 text-blue-600 border-gray-300 focus:ring-blue-500 focus:ring-2"
                />
                <div className="ml-4 flex items-center">
                  <div className="p-2 bg-blue-100 rounded-lg mr-3 group-hover:bg-blue-200 transition-colors">
                    <Settings className="w-4 h-4 text-blue-600" />
                  </div>
                  <div>
                    <div className="font-semibold text-gray-900">Use Own Delivery Service</div>
                    <div className="text-sm text-gray-600">Handle deliveries with your own staff and vehicles</div>
                  </div>
                </div>
              </label>
            </div>
          </div>
        )}

        {/* Save Button */}
        <div className="flex justify-end pt-4">
          <button
            onClick={handleSave}
            disabled={isSaving}
            className="px-8 py-3 bg-gradient-to-r from-emerald-600 to-emerald-700 text-white font-semibold rounded-xl hover:from-emerald-700 hover:to-emerald-800 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 flex items-center gap-2"
          >
            {isSaving ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                Saving...
              </>
            ) : (
              <>
                <Settings className="w-4 h-4" />
                Save Settings
              </>
            )}
          </button>
        </div>
      </div>

      {/* Help Dialog */}
      <Dialog open={showHelpDialog} onOpenChange={setShowHelpDialog}>
        <DialogContent className="sm:max-w-[700px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <HelpCircle className="h-5 w-5 text-blue-600" />
              Delivery Settings Help
            </DialogTitle>
            <DialogDescription>
              Learn how to configure your delivery and pickup options effectively
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 max-h-[60vh] overflow-y-auto">
            {/* Service Options Section */}
            <div className="border border-gray-200 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-200">
              <button
                onClick={() => toggleSection('service-options')}
                className="w-full flex items-center justify-between p-5 text-left hover:bg-gradient-to-r hover:from-emerald-50 hover:to-blue-50 transition-all duration-200 rounded-t-xl"
              >
                <div className="flex items-center">
                  <div className="p-2 bg-emerald-100 rounded-lg mr-3">
                    <Settings className="h-4 w-4 text-emerald-600" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900">Service Options</h3>
                </div>
                {expandedSections['service-options'] ? (
                  <ChevronUp className="h-5 w-5 text-gray-500 transition-transform duration-200" />
                ) : (
                  <ChevronDown className="h-5 w-5 text-gray-500 transition-transform duration-200" />
                )}
              </button>
              {expandedSections['service-options'] && (
                <div className="px-5 pb-5 space-y-4 text-sm text-gray-600 border-t border-gray-100 bg-gray-50/50">
                  <div className="p-3 bg-white rounded-lg border border-gray-200">
                    <strong className="text-gray-900">Pickup Service:</strong> Allows customers to collect orders from your business location. This is always recommended as it gives customers flexibility and reduces your delivery costs.
                  </div>
                  <div className="p-3 bg-white rounded-lg border border-gray-200">
                    <strong className="text-gray-900">Delivery Service:</strong> Enables order delivery to customer locations. You can choose between Loop's delivery service or handle deliveries yourself.
                  </div>
                </div>
              )}
            </div>

            {/* Permanent vs Temporary Section */}
            <div className="border border-gray-200 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-200">
              <button
                onClick={() => toggleSection('permanent-vs-temporary')}
                className="w-full flex items-center justify-between p-5 text-left hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 transition-all duration-200 rounded-t-xl"
              >
                <div className="flex items-center">
                  <div className="p-2 bg-blue-100 rounded-lg mr-3">
                    <Clock className="h-4 w-4 text-blue-600" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900">Permanent vs Temporary Settings</h3>
                </div>
                {expandedSections['permanent-vs-temporary'] ? (
                  <ChevronUp className="h-5 w-5 text-gray-500 transition-transform duration-200" />
                ) : (
                  <ChevronDown className="h-5 w-5 text-gray-500 transition-transform duration-200" />
                )}
              </button>
              {expandedSections['permanent-vs-temporary'] && (
                <div className="px-5 pb-5 space-y-4 text-sm text-gray-600 border-t border-gray-100 bg-gray-50/50">
                  <div className="p-3 bg-white rounded-lg border border-gray-200">
                    <strong className="text-gray-900">Enable Settings:</strong> These are permanent capability settings for your business. They determine what services you can offer long-term.
                  </div>
                  <div className="p-3 bg-white rounded-lg border border-gray-200">
                    <strong className="text-gray-900">Available Settings:</strong> These are temporary operational toggles. Use them when you need to temporarily disable a service (e.g., when delivery drivers are unavailable) without losing your configuration.
                  </div>
                </div>
              )}
            </div>

            {/* Delivery Provider Section */}
            <div className="border border-gray-200 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-200">
              <button
                onClick={() => toggleSection('delivery-provider')}
                className="w-full flex items-center justify-between p-5 text-left hover:bg-gradient-to-r hover:from-purple-50 hover:to-emerald-50 transition-all duration-200 rounded-t-xl"
              >
                <div className="flex items-center">
                  <div className="p-2 bg-purple-100 rounded-lg mr-3">
                    <Truck className="h-4 w-4 text-purple-600" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900">Delivery Service Provider</h3>
                </div>
                {expandedSections['delivery-provider'] ? (
                  <ChevronUp className="h-5 w-5 text-gray-500 transition-transform duration-200" />
                ) : (
                  <ChevronDown className="h-5 w-5 text-gray-500 transition-transform duration-200" />
                )}
              </button>
              {expandedSections['delivery-provider'] && (
                <div className="px-5 pb-5 space-y-4 text-sm text-gray-600 border-t border-gray-100 bg-gray-50/50">
                  <div className="p-3 bg-white rounded-lg border border-gray-200">
                    <strong className="text-gray-900">Loop Delivery:</strong> We handle all deliveries using our network of drivers. You don't need to worry about delivery logistics, driver management, or delivery tracking.
                  </div>
                  <div className="p-3 bg-white rounded-lg border border-gray-200">
                    <strong className="text-gray-900">Own Delivery:</strong> You handle deliveries with your own staff and vehicles. This gives you full control but requires managing drivers, routes, and delivery logistics yourself.
                  </div>
                </div>
              )}
            </div>

            {/* Coming Soon Section */}
            <div className="border border-gray-200 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-200">
              <button
                onClick={() => toggleSection('coming-soon')}
                className="w-full flex items-center justify-between p-5 text-left hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 transition-all duration-200 rounded-t-xl"
              >
                <div className="flex items-center">
                  <div className="p-2 bg-blue-100 rounded-lg mr-3">
                    <Clock className="h-4 w-4 text-blue-600" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900">Coming Soon</h3>
                </div>
                {expandedSections['coming-soon'] ? (
                  <ChevronUp className="h-5 w-5 text-gray-500 transition-transform duration-200" />
                ) : (
                  <ChevronDown className="h-5 w-5 text-gray-500 transition-transform duration-200" />
                )}
              </button>
              {expandedSections['coming-soon'] && (
                <div className="px-5 pb-5 space-y-4 text-sm text-gray-600 border-t border-gray-100 bg-gray-50/50">
                  <div className="p-3 bg-white rounded-lg border border-gray-200">
                    <strong className="text-gray-900">Advanced Features:</strong> Geographic delivery restrictions, custom pricing zones, delivery time slots, and order value limits will be added to this page soon.
                  </div>
                </div>
              )}
            </div>
          </div>

          <DialogFooter>
            <Button onClick={() => setShowHelpDialog(false)}>
              Got it, thanks!
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}
