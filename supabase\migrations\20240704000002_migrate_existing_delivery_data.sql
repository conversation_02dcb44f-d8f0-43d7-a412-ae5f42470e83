-- Migrate existing delivery data to new tables
-- This migration populates the new delivery system tables with existing data

-- 1. Migrate opening hours from JSON to relational structure
-- First, let's create a function to parse the JSON opening hours
CREATE OR REPLACE FUNCTION migrate_opening_hours()
RETURNS void AS $$
DECLARE
  business_record RECORD;
  hours_json JSONB;
  day_name TEXT;
  day_number INTEGER;
  open_time TIME;
  close_time TIME;
BEGIN
  -- Loop through all businesses with opening hours
  FOR business_record IN 
    SELECT id, opening_hours 
    FROM businesses 
    WHERE opening_hours IS NOT NULL
  LOOP
    hours_json := business_record.opening_hours;
    
    -- Process each day of the week
    FOR day_name IN SELECT * FROM jsonb_object_keys(hours_json)
    LOOP
      -- Convert day name to number (0=Sunday, 1=Monday, etc.)
      day_number := CASE day_name
        WHEN 'sunday' THEN 0
        WHEN 'monday' THEN 1
        WHEN 'tuesday' THEN 2
        WHEN 'wednesday' THEN 3
        WHEN 'thursday' THEN 4
        WHEN 'friday' THEN 5
        WHEN 'saturday' THEN 6
        ELSE NULL
      END;
      
      IF day_number IS NOT NULL THEN
        -- Extract open and close times
        IF hours_json->day_name->>'open' IS NOT NULL AND hours_json->day_name->>'open' != '' THEN
          open_time := (hours_json->day_name->>'open')::TIME;
        ELSE
          open_time := NULL;
        END IF;
        
        IF hours_json->day_name->>'close' IS NOT NULL AND hours_json->day_name->>'close' != '' THEN
          close_time := (hours_json->day_name->>'close')::TIME;
        ELSE
          close_time := NULL;
        END IF;
        
        -- Insert into new table
        INSERT INTO business_opening_hours (
          business_id, 
          day_of_week, 
          open_time, 
          close_time,
          effective_date
        ) VALUES (
          business_record.id,
          day_number,
          open_time,
          close_time,
          CURRENT_DATE
        ) ON CONFLICT (business_id, day_of_week, effective_date) DO NOTHING;
      END IF;
    END LOOP;
  END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Execute the migration function
SELECT migrate_opening_hours();

-- Drop the function as it's no longer needed
DROP FUNCTION migrate_opening_hours();

-- 2. Migrate existing delivery configuration
INSERT INTO business_delivery_config (
  business_id,
  pickup_enabled,
  delivery_enabled,
  use_loop_delivery,
  delivery_radius,
  pickup_asap_available,
  pickup_scheduled_available,
  delivery_asap_available,
  delivery_scheduled_available,
  min_advance_booking_minutes,
  max_advance_booking_days,
  minimum_order_value,
  maximum_order_value
)
SELECT 
  id as business_id,
  COALESCE(pickup_available, true) as pickup_enabled,
  COALESCE(delivery_available, false) as delivery_enabled,
  COALESCE(use_loop_delivery, true) as use_loop_delivery,
  delivery_radius,
  COALESCE(pickup_asap_available, true) as pickup_asap_available,
  COALESCE(pickup_scheduled_time_available, false) as pickup_scheduled_available,
  COALESCE(delivery_asap_available, true) as delivery_asap_available,
  COALESCE(delivery_scheduled_time_available, false) as delivery_scheduled_available,
  COALESCE(min_advance_booking_minutes, 30) as min_advance_booking_minutes,
  COALESCE(max_advance_booking_days, 7) as max_advance_booking_days,
  COALESCE(minimum_order_amount, 15.00) as minimum_order_value,
  200.00 as maximum_order_value -- Default max order value for fraud protection
FROM businesses
ON CONFLICT (business_id) DO NOTHING;

-- 3. Migrate existing delivery pricing
INSERT INTO business_delivery_pricing (
  business_id,
  pricing_model,
  base_delivery_fee,
  fee_per_km,
  free_delivery_threshold
)
SELECT 
  id as business_id,
  CASE 
    WHEN delivery_fee_per_km IS NOT NULL AND delivery_fee_per_km > 0 THEN 'distance'
    ELSE 'fixed'
  END as pricing_model,
  COALESCE(delivery_fee, 2.50) as base_delivery_fee,
  delivery_fee_per_km as fee_per_km,
  free_delivery_over as free_delivery_threshold
FROM businesses
WHERE delivery_available = true OR delivery_fee IS NOT NULL
ON CONFLICT (business_id) DO NOTHING;

-- 4. Create default delivery config for businesses without existing config
INSERT INTO business_delivery_config (business_id)
SELECT id 
FROM businesses 
WHERE id NOT IN (SELECT business_id FROM business_delivery_config)
ON CONFLICT (business_id) DO NOTHING;

-- 5. Create default pricing config for businesses with delivery enabled
INSERT INTO business_delivery_pricing (business_id)
SELECT business_id 
FROM business_delivery_config 
WHERE delivery_enabled = true 
  AND business_id NOT IN (SELECT business_id FROM business_delivery_pricing)
ON CONFLICT (business_id) DO NOTHING;

-- 6. Handle temporary closure messages
-- Migrate existing temporary_closure_message to opening hours
UPDATE business_opening_hours 
SET 
  is_closed = true,
  closure_message = b.temporary_closure_message
FROM businesses b
WHERE business_opening_hours.business_id = b.id
  AND b.temporary_closure_message IS NOT NULL 
  AND b.temporary_closure_message != ''
  AND b.is_temporarily_closed = true;

-- Add comment for future reference
COMMENT ON TABLE business_opening_hours IS 'Relational opening hours replacing JSON format in businesses table';
COMMENT ON TABLE business_delivery_config IS 'Core delivery configuration per business';
COMMENT ON TABLE business_parish_delivery IS 'Parish-based delivery restrictions (whitelist approach)';
COMMENT ON TABLE business_delivery_pricing IS 'Flexible delivery pricing configuration';
COMMENT ON TABLE parishes IS 'Jersey parishes reference data for delivery restrictions';
