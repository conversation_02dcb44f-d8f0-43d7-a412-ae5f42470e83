"use client"

import { useState } from 'react'
import { Button } from '@/components/ui/button'

export default function TestAvailabilityPage() {
  const [result, setResult] = useState<any>(null)
  const [loading, setLoading] = useState(false)

  const testAvailability = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/businesses/availability', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          businessIds: ['1', '2', '4', '82'] // Jersey Wings, 2 Tasty, Jersey Grill, Jersey Co-op
        })
      })
      
      const data = await response.json()
      setResult(data)
    } catch (error) {
      setResult({ error: error.message })
    } finally {
      setLoading(false)
    }
  }

  const currentTime = new Date()
  const jerseyTime = new Date().toLocaleString("en-GB", {timeZone: "Europe/London"})

  return (
    <div className="container mx-auto p-8">
      <h1 className="text-2xl font-bold mb-4">Business Availability Test</h1>
      
      <div className="mb-4">
        <p><strong>Current Time:</strong> {currentTime.toString()}</p>
        <p><strong>Jersey Time:</strong> {jerseyTime}</p>
        <p><strong>Current Day:</strong> {currentTime.getDay()} ({['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'][currentTime.getDay()]})</p>
        <p><strong>Current Time (HH:MM):</strong> {currentTime.toTimeString().substring(0, 5)}</p>
      </div>

      <Button onClick={testAvailability} disabled={loading}>
        {loading ? 'Testing...' : 'Test Business Availability'}
      </Button>

      {result && (
        <div className="mt-4">
          <h2 className="text-lg font-semibold mb-2">Results:</h2>
          <pre className="bg-gray-100 p-4 rounded overflow-auto">
            {JSON.stringify(result, null, 2)}
          </pre>
        </div>
      )}
    </div>
  )
}
