"use client"

/**
 * Generic Business Detail Page
 *
 * This page displays details for any type of business.
 * It uses a simplified layout that avoids the section with image, business details,
 * More Info and View Map buttons to prevent caching issues.
 */

import { useState, useEffect, useMemo, useRef, useCallback } from "react"
import React from "react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import AisleBusinessLayout from "@/components/aisle-business-layout"
import { MapPin, Phone, Mail, Info, Star, UtensilsCrossed, ShoppingBag, ChevronLeft, ChevronRight, Search, Tag, X, Map as MapIcon, Clock, Bike, DollarSign, Truck, AlertCircle, Store } from "lucide-react"
import { getBusinessTypeIconWithStyle } from "@/utils/business-type-icons"
import { Button } from "@/components/ui/button"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { getBusinessById } from "@/services/generic-business-service"
import { notFound } from "next/navigation"
import MenuCategory from "@/components/menu-category"
import ProductItem from "@/components/product-item"
import { useRealtimeCart } from "@/context/realtime-cart-context"
import PlaceholderBusinessPage from "@/components/business/placeholder-business-page"
import { CopyButton } from "@/components/copy-button"
import { useBusinessRealtime } from "@/hooks/use-business-realtime"
import BusinessInfoDialog from "@/components/business-info-dialog"
import ClickablePhone from "@/components/clickable-phone"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import CategoryScroller from "@/components/category-scroller"
import { ReviewDisplay } from "@/components/reviews/ReviewDisplay"
import OSMRestaurantMap from "@/components/osm-restaurant-map"
import FallbackImage from "@/components/fallback-image"
import BusinessLocationMap from "@/components/delivery/business-location-map"
import { useSupabaseRealtime } from "@/hooks/use-supabase-realtime"
import { BusinessClosedDialog } from "@/components/business/business-closed-dialog"
import { supabase, supabaseRealtime } from "@/lib/supabase"
import CheckoutLink from "@/components/checkout-link"
import {
  calculateDistance,
  calculateDeliveryFeeWithDistance,
  getUserCoordinates,
  formatDeliveryFee
} from '@/lib/distance-calculation'
import { useDeliveryCalculation } from '@/hooks/use-delivery-calculation'

// Default coordinates for St Helier (center of Jersey)
const DEFAULT_COORDINATES: [number, number] = [-2.1053, 49.1805];

// Default delivery radius in kilometers
const DEFAULT_DELIVERY_RADIUS = 5;

// Helper function to format variant and customization details (same as cart-sheet.tsx)
const formatItemDetails = (item: any): string[] => {
  const details: string[] = [];

  // Add variant information
  if (item.variantName) {
    details.push(`Size: ${item.variantName}`);
  }

  // Add customization information
  if (item.customizations && item.customizations.length > 0) {
    item.customizations.forEach((customization: any) => {
      if (customization.options && customization.options.length > 0) {
        const optionNames = customization.options.map((option: any) => {
          if (option.price > 0) {
            return `${option.name} (+£${option.price.toFixed(2)})`;
          }
          return option.name;
        });
        details.push(`${customization.groupName}: ${optionNames.join(', ')}`);
      }
    });
  }

  // Fallback to legacy options if no variant/customization data
  if (details.length === 0 && item.options && item.options.length > 0) {
    details.push(...item.options);
  }

  return details;
}

export default function BusinessPage({ params }: { params: { id: string } }) {
  // Unwrap params with React.use() as recommended by Next.js
  const unwrappedParams = React.use(params as any) as { id: string }
  const id = unwrappedParams.id
  const router = useRouter()

  const [business, setBusiness] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const {
    cart,
    getItemsByBusiness,
    getDeliveryMethod,
    setDeliveryMethod: setCartDeliveryMethod,
    getDeliveryFee,
    setDeliveryFee: setCartDeliveryFee,
    setPreparationTime
  } = useRealtimeCart()
  const [businessItems, setBusinessItems] = useState<any[]>([])
  const [businessSubtotal, setBusinessSubtotal] = useState(0)
  const [activeCategory, setActiveCategory] = useState<string>("")
  const [searchQuery, setSearchQuery] = useState("")
  const [showInfoDialog, setShowInfoDialog] = useState(false)
  const [showOSMMapDialog, setShowOSMMapDialog] = useState(false)
  // PHASE 4 STEP 9: Business closure state
  const [showClosedDialog, setShowClosedDialog] = useState(false)
  const [businessClosure, setBusinessClosure] = useState<{
    isTemporarilyClosed: boolean;
    closureMessage: string;
  } | null>(null)

  // Ref to track if we're programmatically scrolling
  const isScrolling = useRef(false)

  // State for coordinates and delivery radius with default values
  const [coordinates, setCoordinates] = useState<[number, number]>(DEFAULT_COORDINATES)
  const [deliveryRadius, setDeliveryRadius] = useState<number>(DEFAULT_DELIVERY_RADIUS)

  // State for business coordinates from the database
  const [businessCoordinates, setBusinessCoordinates] = useState<[number, number] | null>(null)

  // State for preparation time with direct management
  const [prepTime, setPrepTime] = useState<number | null>(null)
  const [prepTimeLoading, setPrepTimeLoading] = useState(true)
  const [prepTimeError, setPrepTimeError] = useState<string | null>(null)

  // Simple useEffect to load business data
  useEffect(() => {
    if (!id) return;

    const loadBusiness = async () => {
      try {
        const businessData = await getBusinessById(id);
        setBusiness(businessData); // Set business data (could be null if not found)
        setLoading(false);
      } catch (error) {
        console.error('Error loading business:', error);
        setBusiness(null);
        setLoading(false);
      }
    };

    loadBusiness();
  }, [id]);

  // State to track if user has manually selected a delivery method
  // This prevents automatic overrides of user choice
  const [userHasSelectedMethod, setUserHasSelectedMethod] = useState(false)

  // Local state for immediate UI feedback (syncs with cart context)
  const [localDeliveryMethod, setLocalDeliveryMethod] = useState<'delivery' | 'pickup' | null>(null)

  // Get real-time business data updates
  const { prepTime: realtimePrepTime, deliveryAvailable: realtimeDeliveryAvailable } = useBusinessRealtime(id)

  // PHASE 4 STEP 9: Function to fetch business closure status
  const fetchBusinessClosure = useCallback(async (businessId: number) => {
    try {
      const response = await fetch(`/api/business-admin/status?businessId=${businessId}`)
      if (response.ok) {
        const result = await response.json()
        if (result.success) {
          const closureData = {
            isTemporarilyClosed: result.data.isTemporarilyClosed,
            closureMessage: result.data.closureMessage || ""
          }
          setBusinessClosure(closureData)

          // Show the closure dialog if business is closed
          if (closureData.isTemporarilyClosed) {
            setShowClosedDialog(true)
          }
        }
      }
    } catch (error) {
      console.error('Error fetching business closure status:', error)
    }
  }, [])

  // Check if user already has a delivery method set for this business on component load
  useEffect(() => {
    if (business?.id) {
      const existingMethod = getDeliveryMethod(business.id)
      const businessItems = getItemsByBusiness(business.id)

      // For errand businesses, force pickup method and don't show delivery options UI
      if (business.businessType === 'errand') {
        console.log(`📋 Errand business detected (${business.name}), forcing pickup method`)
        setCartDeliveryMethod(business.id, 'pickup')
        setLocalDeliveryMethod('pickup')
        return
      }

      // Only mark as user-selected if user has items in cart (indicating they've interacted with the business)
      // Don't mark as user-selected just because method is pickup (could be automatic due to delivery unavailable)
      if (businessItems && businessItems.length > 0) {
        console.log(`📋 Found existing cart items for business ${business.id}: items=${businessItems.length}, marking as user-selected`)
        setUserHasSelectedMethod(true)
      }

      // Initialize local state with cart context value only if not already set
      if (localDeliveryMethod === null) {
        console.log(`📋 Initializing local delivery method: ${existingMethod}`)
        setLocalDeliveryMethod(existingMethod)
      }
    }
  }, [business?.id, business?.businessType]) // Removed function dependencies to prevent infinite loop

  // Get the delivery method from cart context
  const cartDeliveryMethod = getDeliveryMethod(business?.id) // Use numeric business ID directly

  // Use local state for immediate UI feedback, fallback to cart context
  const deliveryMethod = localDeliveryMethod || cartDeliveryMethod

  // Sync local state with cart context when cart context changes
  // But only if user hasn't made a manual selection recently
  useEffect(() => {
    if (business?.id && localDeliveryMethod === null) {
      const cartMethod = getDeliveryMethod(business.id)
      console.log(`🔄 Initializing local delivery method from cart context: ${cartMethod}`)
      setLocalDeliveryMethod(cartMethod)
    }
  }, [business?.id, cartDeliveryMethod, localDeliveryMethod]) // Removed getDeliveryMethod to prevent infinite loop

  // State for delivery calculation results
  const [deliveryData, setDeliveryData] = useState({
    fee: 'Calculating...',
    feeNumeric: 0,
    time: 'Calculating...',
    timeRange: 'Calculating...',
    travelTimeMinutes: 0,
    totalTimeMinutes: 0,
    isLoading: true
  })

  // Calculate delivery fee when business data loads or fee model changes
  useEffect(() => {
    const calculateDeliveryFee = async () => {
      if (!business || !businessCoordinates) {
        console.log('Missing business data or coordinates for delivery calculation')
        return
      }

      try {
        setDeliveryData(prev => ({ ...prev, isLoading: true }))

        const { getUserLocationData, calculateDeliveryEstimates } = await import('@/lib/delivery-calculation-service')

        const { postcode, coordinates } = getUserLocationData()

        if (!postcode || !coordinates) {
          setDeliveryData({
            fee: 'Location required',
            feeNumeric: 0,
            time: 'Unknown',
            timeRange: 'Set location first',
            travelTimeMinutes: 0,
            totalTimeMinutes: 0,
            isLoading: false
          })
          return
        }

        const params = {
          businessId: business.id || id,
          businessName: business.name || '',
          businessCoordinates,
          preparationTimeMinutes: realtimePrepTime !== null ? realtimePrepTime : (business.preparationTimeMinutes || business.preparation_time_minutes || 15),
          deliveryFeeModel: business.delivery_fee_model || 'fixed',
          deliveryFee: business.deliveryFee || business.delivery_fee || 2.50,
          deliveryFeePerKm: business.delivery_fee_per_km || 0.50,
          customerPostcode: postcode,
          customerCoordinates: coordinates
        }

        console.log('Calculating delivery fee with params:', params)
        const result = await calculateDeliveryEstimates(params)

        setDeliveryData({
          ...result,
          isLoading: false
        })

        // Automatically save the calculated delivery fee to cart context if delivery method is set to delivery
        if (result.feeNumeric && typeof result.feeNumeric === 'number' && business?.id) {
          const currentDeliveryMethod = getDeliveryMethod(business.id) // Use numeric business ID
          if (currentDeliveryMethod === 'delivery') {
            setCartDeliveryFee(business.id, result.feeNumeric) // Use numeric business ID
          }
        }

      } catch (error) {
        setDeliveryData(prev => ({
          ...prev,
          fee: 'Error calculating',
          isLoading: false
        }))
      }
    }

    calculateDeliveryFee()
  }, [
    business?.id,
    business?.name,
    business?.delivery_fee_model,
    business?.deliveryFee,
    business?.delivery_fee,
    business?.delivery_fee_per_km,
    business?.preparationTimeMinutes,
    business?.preparation_time_minutes,
    businessCoordinates,
    realtimePrepTime,
    id
  ])

  // Recalculate when user location changes
  useEffect(() => {
    const handleLocationChange = () => {
      console.log('User location changed, recalculating delivery fee...')
      // Trigger recalculation by updating a dependency
      if (business && businessCoordinates) {
        // The calculation will be triggered by the effect above
        setDeliveryData(prev => ({ ...prev, isLoading: true }))
      }
    }

    // Listen for location changes
    window.addEventListener('userLocationChanged', handleLocationChange)

    return () => {
      window.removeEventListener('userLocationChanged', handleLocationChange)
    }
  }, [business, businessCoordinates])



  // TEMPORARILY DISABLED - Update cart context when delivery data changes
  // This useEffect was causing continuous API calls to /api/cart-direct
  // TODO: Re-enable after fixing the continuous syncing issue
  /*
  useEffect(() => {
    if (deliveryData && !deliveryData.isLoading && business?.id) {
      const deliveryTimeValue = deliveryData.totalTimeMinutes;

      console.log(`🚚 BUSINESS PAGE: Updating cart context with delivery time: ${deliveryTimeValue} for business ID: ${business.id}`);
      // Only update delivery time - fee should only be set when user chooses delivery method
      setDeliveryTime(business.id.toString(), deliveryTimeValue);
    }
  }, [deliveryData, business?.id, setDeliveryTime]);
  */

  // Update preparation time in cart context when it changes
  useEffect(() => {
    if (prepTime !== null && business?.id) {
      console.log(`Preparation time updated: ${prepTime}, updating cart context for business ID: ${business.id}`);
      setPreparationTime(business.id, prepTime); // 🔧 FIX: Use numeric business ID
    }
  }, [prepTime, business?.id]) // Removed setPreparationTime to prevent infinite loop

  // Fetch and subscribe to preparation time directly
  useEffect(() => {
    if (!id || !business) {
      setPrepTimeLoading(false)
      return
    }

    setPrepTimeLoading(true)
    setPrepTimeError(null)

    // No initial API fetch needed - real-time subscription will provide the data
    console.log('Setting up prep time real-time subscription without initial fetch...');

    // Set up a dedicated channel for preparation time updates
    const prepTimeChannel = supabaseRealtime
      .channel(`prep-time-${id}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'businesses',
          filter: `slug=eq.${id}`
        },
        (payload) => {
          console.log('Received real-time update for preparation time:', payload)
          if (payload.new && payload.new.preparation_time_minutes !== undefined) {
            console.log(`Setting prep time to ${payload.new.preparation_time_minutes}`)
            setPrepTime(payload.new.preparation_time_minutes)
          }
        }
      )
      .subscribe(async (status) => {
        console.log(`Prep time subscription status: ${status}`)

        // When subscription is established, fetch initial prep time data once
        if (status === 'SUBSCRIBED') {
          console.log('Prep time subscription established, fetching initial data...');
          try {
            const { data, error } = await supabaseRealtime
              .from('businesses')
              .select('preparation_time_minutes')
              .eq('slug', id)
              .single()

            if (error) {
              console.error('Error fetching preparation time:', error)
              setPrepTimeError('Failed to fetch preparation time')
            } else if (data) {
              console.log('Initial preparation time:', data.preparation_time_minutes)
              setPrepTime(data.preparation_time_minutes)
            }
          } catch (err) {
            console.error('Exception fetching preparation time:', err)
            setPrepTimeError('An error occurred while fetching preparation time')
          } finally {
            setPrepTimeLoading(false)
          }
        }
      })

    // Clean up subscription on unmount
    return () => {
      console.log(`Cleaning up prep time subscription for business ${id}...`)
      supabaseRealtime.removeChannel(prepTimeChannel)
    }
  }, [id, business])

  // Generate a discount code based on business name
  const discountCode = useMemo(() => {
    if (!business || !business.name) return "WELCOME25";
    const name = business.name.toUpperCase().replace(/[^A-Z]/g, "")
    return `${name.substring(0, 4)}25`
  }, [business])



  // Create a flat list of all menu items for search functionality
  const allMenuItems = useMemo(() => {
    if (!business || !business.menuCategories) return [];
    return business.menuCategories.flatMap(category =>
      category.items.map(item => ({
        ...item,
        categoryId: category.id,
        categoryName: category.name
      }))
    )
  }, [business])

  // Filter menu items based on search query
  const filteredMenuItems = useMemo(() => {
    if (!searchQuery.trim() || !allMenuItems.length) return null;

    const query = searchQuery.toLowerCase().trim()
    return allMenuItems.filter(item =>
      item.name.toLowerCase().includes(query) ||
      (item.description && item.description.toLowerCase().includes(query)) ||
      item.categoryName.toLowerCase().includes(query)
    )
  }, [searchQuery, allMenuItems])

  // No initial API fetch needed - real-time subscription will handle all data loading
  useEffect(() => {
    console.log(`Setting up real-time-only data loading for business ${id}...`);
    // Real-time subscriptions will handle all data loading
    // Just set a timeout to stop loading if no data comes through
    const timer = setTimeout(() => {
      if (!business) {
        console.log("No business data received from real-time subscriptions, stopping loading...");
        setLoading(false);
      }
    }, 5000); // 5 second timeout

    return () => clearTimeout(timer);
  }, [id, business])

  // Set up a real-time subscription for business updates
  useEffect(() => {
    if (!id) return;

    // Set up real-time subscription for this business
    const businessChannel = supabaseRealtime
      .channel(`business-${id}`)
      .on(
        'postgres_changes',
        {
          event: '*', // Listen for all events (INSERT, UPDATE, DELETE)
          schema: 'public',
          table: 'businesses',
          filter: `slug=eq.${id}`
        },
        async (payload) => {
          console.log('Received real-time update for business:', payload);

          // Immediately update specific fields that we know have changed
          if (payload.new) {
            // Create a shallow copy of the current business
            const updatedBusiness = { ...business };

            // Update delivery fee if it changed
            if (payload.new.delivery_fee !== undefined) {
              console.log(`Updating delivery fee from ${updatedBusiness.deliveryFee} to ${payload.new.delivery_fee}`);
              updatedBusiness.deliveryFee = payload.new.delivery_fee;

              // Also update the formatted version
              if (payload.new.delivery_fee === 0) {
                updatedBusiness.deliveryFeeFormatted = "Free";
              } else {
                updatedBusiness.deliveryFeeFormatted = `£${payload.new.delivery_fee.toFixed(2)}`;
              }
            }

            // Update delivery fee model if it changed
            if (payload.new.delivery_fee_model !== undefined) {
              console.log(`Updating delivery fee model from ${updatedBusiness.delivery_fee_model} to ${payload.new.delivery_fee_model}`);
              updatedBusiness.delivery_fee_model = payload.new.delivery_fee_model;
            }

            // Update delivery fee per km if it changed
            if (payload.new.delivery_fee_per_km !== undefined) {
              console.log(`Updating delivery fee per km from ${updatedBusiness.delivery_fee_per_km} to ${payload.new.delivery_fee_per_km}`);
              updatedBusiness.delivery_fee_per_km = payload.new.delivery_fee_per_km;
            }

            // Update preparation time if it changed
            if (payload.new.preparation_time_minutes !== undefined) {
              console.log(`Updating preparation time from ${updatedBusiness.preparationTimeMinutes} to ${payload.new.preparation_time_minutes}`);
              updatedBusiness.preparationTimeMinutes = payload.new.preparation_time_minutes;

              // Note: We don't need to update prepTime here as we have a dedicated subscription for it
            }

            // Update rating if it changed
            if (payload.new.rating !== undefined) {
              updatedBusiness.rating = payload.new.rating;
            }

            // Update review count if it changed
            if (payload.new.review_count !== undefined) {
              updatedBusiness.reviewCount = payload.new.review_count;
            }

            // Update coordinates if they changed
            if (payload.new.coordinates) {
              setCoordinates(payload.new.coordinates);

              // Also update business coordinates for delivery fee calculation
              try {
                // Parse coordinates if they're in string format
                if (typeof payload.new.coordinates === 'string') {
                  if (payload.new.coordinates.startsWith('[') && payload.new.coordinates.endsWith(']')) {
                    // JSON array format
                    const coords = JSON.parse(payload.new.coordinates);
                    if (Array.isArray(coords) && coords.length === 2) {
                      setBusinessCoordinates([parseFloat(coords[0]), parseFloat(coords[1])]);
                    }
                  } else if (payload.new.coordinates.startsWith('(') && payload.new.coordinates.endsWith(')')) {
                    // Parentheses format like "(lng,lat)"
                    const coordsStr = payload.new.coordinates.substring(1, payload.new.coordinates.length - 1);
                    const parts = coordsStr.split(',').map(part => parseFloat(part.trim()));
                    if (parts.length === 2 && !isNaN(parts[0]) && !isNaN(parts[1])) {
                      setBusinessCoordinates([parts[0], parts[1]]);
                    }
                  } else {
                    // Simple comma-separated format
                    const parts = payload.new.coordinates.split(',').map(part => parseFloat(part.trim()));
                    if (parts.length === 2 && !isNaN(parts[0]) && !isNaN(parts[1])) {
                      setBusinessCoordinates([parts[0], parts[1]]);
                    }
                  }
                } else if (Array.isArray(payload.new.coordinates) && payload.new.coordinates.length === 2) {
                  // Array format
                  setBusinessCoordinates([
                    parseFloat(payload.new.coordinates[0]),
                    parseFloat(payload.new.coordinates[1])
                  ]);
                }
              } catch (parseError) {
                console.error('Error parsing business coordinates:', parseError);
              }
            }

            // Update delivery radius if it changed
            if (payload.new.delivery_radius) {
              setDeliveryRadius(payload.new.delivery_radius);
            }

            // PHASE 4 STEP 9: Update business closure status if it changed
            if (payload.new.is_temporarily_closed !== undefined || payload.new.closure_message !== undefined) {
              console.log(`Updating business closure status: closed=${payload.new.is_temporarily_closed}, message="${payload.new.closure_message}"`);
              setBusinessClosure({
                isTemporarilyClosed: payload.new.is_temporarily_closed || false,
                closureMessage: payload.new.closure_message || ""
              });
            }

            // Update the business state with our changes
            setBusiness(updatedBusiness);
          }

          // Real-time update complete - no additional API call needed
        }
      )
      .subscribe(async (status) => {
        console.log(`Supabase channel status for business: ${status}`);

        // When subscription is established, fetch initial data once
        if (status === 'SUBSCRIBED') {
          console.log('Business subscription established, fetching initial data...');
          try {
            const freshData = await getBusinessById(id);
            if (freshData) {
              console.log("Initial business data from real-time subscription:", freshData);
              console.log("Menu categories:", freshData.menuCategories);
              if (freshData.menuCategories && freshData.menuCategories.length > 0) {
                console.log("First category items:", freshData.menuCategories[0].items);




                if (freshData.menuCategories[0].items && freshData.menuCategories[0].items.length > 0) {
                  const firstItem = freshData.menuCategories[0].items[0];
                  console.log("First item details:", firstItem);
                  console.log("First item variants:", firstItem.variants);
                  console.log("First item customizations:", firstItem.customizations);
                }
              }
              // Note: Aisle layout businesses will be handled by conditional rendering below

              setBusiness(freshData);
              setLoading(false);

              // PHASE 4 STEP 9: Fetch business closure status when business data loads
              if (freshData.id) {
                fetchBusinessClosure(freshData.id);
              }

              // Menu items will be automatically computed via useMemo when business data updates
              console.log("Business data loaded, menu items will be computed automatically");

              // Set coordinates from database if available
              if (freshData.coordinates) {
                // Parse coordinates if they're in string format
                if (typeof freshData.coordinates === 'string') {
                  try {
                    // Try to parse PostgreSQL POINT format: "POINT(longitude latitude)"
                    const pointMatch = freshData.coordinates.match(/POINT\s*\(\s*([^\s]+)\s+([^\s]+)\s*\)/i);
                    if (pointMatch && pointMatch.length === 3) {
                      const lng = parseFloat(pointMatch[1]);
                      const lat = parseFloat(pointMatch[2]);
                      if (!isNaN(lng) && !isNaN(lat)) {
                        setCoordinates([lng, lat]);
                        setBusinessCoordinates([lng, lat]);
                        console.log(`Parsed POINT format coordinates: [${lng}, ${lat}]`);
                      }
                    } else {
                      // Try to parse parentheses format: "(longitude,latitude)"
                      const parenMatch = freshData.coordinates.match(/\(\s*([-\d.]+)\s*,\s*([-\d.]+)\s*\)/);
                      if (parenMatch && parenMatch.length === 3) {
                        const lng = parseFloat(parenMatch[1]);
                        const lat = parseFloat(parenMatch[2]);
                        if (!isNaN(lng) && !isNaN(lat)) {
                          setCoordinates([lng, lat]);
                          setBusinessCoordinates([lng, lat]);
                          console.log(`Parsed parentheses format coordinates: [${lng}, ${lat}]`);
                        }
                      }
                    }
                  } catch (error) {
                    console.error("Error parsing coordinates string:", error);
                  }
                } else {
                  setCoordinates(freshData.coordinates);
                  setBusinessCoordinates(freshData.coordinates);
                }
              }

              // Set delivery radius from database if available
              if (freshData.delivery_radius) {
                setDeliveryRadius(freshData.delivery_radius)
              }
            }
          } catch (error) {
            console.error("Error fetching initial business data:", error);
            setLoading(false);
          }
        }
      });

    // Clean up subscription on unmount
    return () => {
      console.log(`Cleaning up real-time subscription for business ${id}...`);
      supabaseRealtime.removeChannel(businessChannel);
    };
  }, [id]); // Removed 'business' dependency to prevent infinite loop

  // Set up a real-time subscription for product updates
  useEffect(() => {
    if (!id || !business || !business.id) return;

    console.log(`Setting up real-time subscription for products of business ${id}...`);

    // Set up real-time subscription for products of this business
    const productsChannel = supabaseRealtime
      .channel(`products-${id}`)
      .on(
        'postgres_changes',
        {
          event: '*', // Listen for all events (INSERT, UPDATE, DELETE)
          schema: 'public',
          table: 'products',
          filter: `business_id=eq.${business.id}`
        },
        async (payload) => {
          console.log('Received real-time update for product:', payload);

          // Product updates should trigger a re-fetch of business data to get updated menu
          // But we'll handle this through the business real-time subscription instead
          console.log('Product update received - business subscription will handle menu updates');
        }
      )
      .subscribe((status) => {
        console.log(`Supabase channel status for products: ${status}`);
      });

    // Clean up subscription on unmount
    return () => {
      console.log(`Cleaning up real-time subscription for products of business ${id}...`);
      supabaseRealtime.removeChannel(productsChannel);
    };
  }, [id, business?.id]);





  // Get items and calculate subtotal for this specific business
  useEffect(() => {
    if (!business) return;

    const itemsByBusiness = getItemsByBusiness()
    const items = itemsByBusiness[business.id] || []
    setBusinessItems(items)

    const subtotal = items.reduce((total, item) => {
      return total + item.price * item.quantity
    }, 0)
    setBusinessSubtotal(subtotal)
  }, [business, cart]) // Removed getItemsByBusiness to prevent infinite loop

  // Set initial active category when component mounts
  useEffect(() => {
    if (!business || !business.menuCategories) return;

    if (business.menuCategories.length > 0) {
      setActiveCategory(business.menuCategories[0].id)
    }
  }, [business])





  // Handle delivery method toggle - simplified since centralized service handles calculations
  const handleDeliveryMethodChange = useCallback(async (method: 'delivery' | 'pickup') => {
    if (method !== deliveryMethod && business?.id) {
      console.log(`🎯 User manually selected ${method} for business ${business.id}`)

      // Set local state immediately for instant UI feedback
      setLocalDeliveryMethod(method)

      // Mark that user has made a manual selection
      setUserHasSelectedMethod(true)

      setCartDeliveryMethod(business.id, method); // 🔧 FIX: Use numeric business ID

      // If switching to delivery and we have a calculated delivery fee, save it to cart context
      if (method === 'delivery' && deliveryData?.feeNumeric && typeof deliveryData.feeNumeric === 'number' && business?.id) {
        console.log(`💰 Saving delivery fee £${deliveryData.feeNumeric.toFixed(2)} to cart after switching to delivery for business ${business.id}`)
        setCartDeliveryFee(business.id, deliveryData.feeNumeric) // Use numeric business ID
      }

      // If switching to pickup, set delivery fee to 0
      if (method === 'pickup' && business?.id) {
        console.log(`💰 Setting delivery fee to £0.00 after switching to pickup for business ${business.id}`)
        setCartDeliveryFee(business.id, 0) // Use numeric business ID
      }
    }
  }, [deliveryMethod, deliveryData, business?.id]); // Removed function dependencies to prevent infinite loop

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  // Handle category change from the category scroller
  const handleCategoryChange = (categoryId: string) => {
    setActiveCategory(categoryId);

    // Scroll to the selected category
    const element = document.getElementById(`category-${categoryId}`);
    if (element) {
      isScrolling.current = true;
      element.scrollIntoView({ behavior: 'smooth', block: 'start' });

      // Reset the scrolling flag after animation completes
      setTimeout(() => {
        isScrolling.current = false;
      }, 1000);
    }
  };

  // Update delivery method when delivery availability changes
  useEffect(() => {
    // Skip for errand businesses
    if (business?.businessType === 'errand') {
      return
    }

    // Only auto-switch to pickup if delivery is not available
    // But don't run this effect if user has already made a manual selection
    if (userHasSelectedMethod) {
      console.log(`👤 User has manually selected method, skipping automatic delivery availability changes`)
      return
    }

    const deliveryNotAvailable = realtimeDeliveryAvailable === false || (realtimeDeliveryAvailable === null && business?.delivery_available === false)

    if (deliveryNotAvailable && business?.id) {
      console.log(`🚫 Delivery not available for business ${business.id}, auto-switching to pickup`)
      setLocalDeliveryMethod('pickup') // Update local state immediately
      setCartDeliveryMethod(business.id, 'pickup') // Also update cart context
    } else if (!deliveryNotAvailable && business?.id) {
      // If delivery is available and no method is set, default to pickup
      const currentMethod = getDeliveryMethod(business.id)
      if (!currentMethod || currentMethod === null) {
        console.log(`✅ No delivery method set for business ${business.id}, defaulting to pickup`)
        setLocalDeliveryMethod('pickup')
        setCartDeliveryMethod(business.id, 'pickup')
      }
    }
  }, [realtimeDeliveryAvailable, business?.delivery_available, business?.id, business?.businessType, userHasSelectedMethod]) // Removed function dependencies to prevent infinite loop

  // Ensure delivery fee is saved to cart when delivery method is set to delivery
  useEffect(() => {
    if (deliveryMethod === 'delivery' && deliveryData?.feeNumeric && typeof deliveryData.feeNumeric === 'number' && business?.id) {
      const currentCartFee = getDeliveryFee(business.id) // Use numeric business ID directly
      // Only update if the cart fee is different from the calculated fee
      if (Math.abs(currentCartFee - deliveryData.feeNumeric) > 0.01) {
        console.log(`💰 Syncing delivery fee to cart: £${deliveryData.feeNumeric.toFixed(2)} (was £${currentCartFee.toFixed(2)})`)
        setCartDeliveryFee(business.id, deliveryData.feeNumeric) // Use numeric business ID directly
      }
    }
  }, [deliveryMethod, deliveryData?.feeNumeric, business?.id]) // No function dependencies here

  // Listen for custom events from cart context to trigger delivery fee calculation
  useEffect(() => {
    const handleCalculateDeliveryFee = (event: CustomEvent) => {
      const { businessId } = event.detail;
      console.log(`📡 Received calculateDeliveryFee event for business ${businessId}`);

      // Check if this event is for the current business
      if (business?.id && businessId === business.id) {
        console.log(`💰 Triggering delivery fee calculation for business ${business.id}`);

        // If we already have delivery data and the method is delivery, set the fee immediately
        if (deliveryMethod === 'delivery' && deliveryData?.feeNumeric && typeof deliveryData.feeNumeric === 'number') {
          console.log(`💰 Setting delivery fee from existing data: £${deliveryData.feeNumeric.toFixed(2)}`);
          setCartDeliveryFee(business.id, deliveryData.feeNumeric);
        }
      }
    };

    // Add event listener
    window.addEventListener('calculateDeliveryFee', handleCalculateDeliveryFee as EventListener);

    // Cleanup
    return () => {
      window.removeEventListener('calculateDeliveryFee', handleCalculateDeliveryFee as EventListener);
    };
  }, [business?.id, deliveryMethod, deliveryData?.feeNumeric])

  // Scroll spy functionality to update active category based on scroll position
  useEffect(() => {
    if (!business || !business.menuCategories) return;

    const handleScroll = () => {
      // Don't run if we're programmatically scrolling
      if (isScrolling.current) return

      const categoryElements = business.menuCategories.map(category =>
        document.getElementById(`category-${category.id}`)
      )

      // Find the category that is currently most visible in the viewport
      let mostVisibleCategory = null
      let maxVisibleHeight = 0

      categoryElements.forEach((element, index) => {
        if (!element) return

        const rect = element.getBoundingClientRect()
        const visibleTop = Math.max(rect.top, 200) // 200px is the navbar + sticky header height
        const visibleBottom = Math.min(rect.bottom, window.innerHeight)
        const visibleHeight = Math.max(0, visibleBottom - visibleTop)

        if (visibleHeight > maxVisibleHeight) {
          maxVisibleHeight = visibleHeight
          mostVisibleCategory = business.menuCategories[index].id
        }
      })

      if (mostVisibleCategory && mostVisibleCategory !== activeCategory) {
        setActiveCategory(mostVisibleCategory)
      }
    }

    window.addEventListener('scroll', handleScroll)
    return () => {
      window.removeEventListener('scroll', handleScroll)
    }
  }, [business, activeCategory])

  // Function to scroll to a category section
  const scrollToCategory = useCallback((categoryId: string) => {
    const element = document.getElementById(`category-${categoryId}`)
    if (element) {
      // Set flag to prevent scroll spy from running during programmatic scroll
      isScrolling.current = true

      // Offset for the navbar + sticky header (search + categories)
      const offset = 200
      const elementPosition = element.getBoundingClientRect().top
      const offsetPosition = elementPosition + window.pageYOffset - offset

      window.scrollTo({
        top: offsetPosition,
        behavior: "smooth"
      })

      // Reset the flag after animation completes (roughly 500ms)
      setTimeout(() => {
        isScrolling.current = false
      }, 500)
    }
  }, [])

  // Function to scroll to reviews section
  const scrollToReviews = useCallback(() => {
    const element = document.getElementById('customer-reviews')
    if (element) {
      // Offset for the navbar
      const offset = 100
      const elementPosition = element.getBoundingClientRect().top
      const offsetPosition = elementPosition + window.pageYOffset - offset

      window.scrollTo({
        top: offsetPosition,
        behavior: "smooth"
      })
    }
  }, [])

  // Show loading state while fetching data
  if (loading || prepTimeLoading) {
    return (
      <div className="container-fluid py-12 text-center">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-100 rounded w-1/3 mx-auto mb-4"></div>
          <div className="h-64 bg-gray-100 rounded mb-6 border border-gray-100"></div>
          <div className="h-4 bg-gray-100 rounded w-1/2 mx-auto mb-2"></div>
          <div className="h-4 bg-gray-100 rounded w-1/3 mx-auto"></div>
        </div>
      </div>
    )
  }

  // Show 404 if business not found and loading is complete
  if (!loading && !business) {
    notFound()
  }

  // Show error state if any errors occurred (only if business exists)
  if (business && prepTimeError) {
    return (
      <div className="container-fluid py-12 text-center">
        <div className="bg-red-50 p-4 rounded-lg border border-red-200 mb-4">
          <h2 className="text-red-700 text-lg font-semibold mb-2">Error Loading Data</h2>
          <p className="text-red-600">{prepTimeError}</p>
        </div>
        <Button onClick={() => window.location.reload()} className="mt-4">
          Retry
        </Button>
      </div>
    )
  }

  // Handle placeholder businesses
  if (business.isPlaceholder || business.status === 'placeholder') {
    return <PlaceholderBusinessPage business={business} />
  }

  // Handle aisle layout businesses
  if (business.page_layout === 'aisle') {
    return (
      <AisleBusinessLayout
        business={business}
        isInfoDialogOpen={showInfoDialog}
        setIsInfoDialogOpen={setShowInfoDialog}
        isMapDialogOpen={showOSMMapDialog}
        setIsMapDialogOpen={setShowOSMMapDialog}
      />
    );
  }

  return (
    <div className="relative">
      {/* PHASE 4 STEP 9: Business closure modal dialog */}
      {businessClosure?.isTemporarilyClosed && showClosedDialog && (
        <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
            <div className="text-center">
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
                <AlertCircle className="h-6 w-6 text-red-600" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {business.name} is temporarily closed
              </h3>
              {businessClosure.closureMessage ? (
                <p className="text-sm text-gray-600 mb-4">
                  {businessClosure.closureMessage}
                </p>
              ) : (
                <p className="text-sm text-gray-600 mb-4">
                  This business is currently not accepting new orders. Please check back later.
                </p>
              )}
              <p className="text-sm text-gray-500 mb-4">
                You can still browse their menu, but ordering is temporarily disabled.
              </p>
              <div className="flex gap-3">
                <Button
                  onClick={() => setShowClosedDialog(false)}
                  variant="outline"
                  className="flex-1"
                >
                  Browse Menu
                </Button>
                <Button
                  onClick={() => window.history.back()}
                  className="flex-1"
                >
                  Go Back
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* PHASE 4 STEP 9: Business closure banner when browsing menu */}
      {businessClosure?.isTemporarilyClosed && !showClosedDialog && (
        <div className="bg-red-50 border-b border-red-200 p-4">
          <div className="container-fluid">
            <div className="flex items-center gap-3">
              <AlertCircle className="h-5 w-5 text-red-600 flex-shrink-0" />
              <div className="flex-1">
                <p className="text-red-800 font-medium">
                  {business.name} is temporarily closed
                </p>
                {businessClosure.closureMessage && (
                  <p className="text-red-700 text-sm mt-1">
                    {businessClosure.closureMessage}
                  </p>
                )}
              </div>
              <Button
                onClick={() => window.history.back()}
                variant="outline"
                size="sm"
                className="border-red-300 text-red-700 hover:bg-red-100"
              >
                Go Back
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* PHASE 4 STEP 9: Veil overlay when business is closed and browsing menu */}
      {businessClosure?.isTemporarilyClosed && !showClosedDialog && (
        <div className="fixed inset-0 bg-black/20 z-40 pointer-events-none" />
      )}

      {/* Main content with conditional pointer events */}
      <div className={businessClosure?.isTemporarilyClosed && !showClosedDialog ? "pointer-events-none" : ""}>
        {/* Business Header Section */}
      <div className="container-fluid mb-4 mt-2">
        <div className="flex flex-col md:flex-row gap-6">
          {/* Left Column - Business Logo */}
          <div className="w-full md:w-1/3">
            <div className="relative rounded-lg overflow-hidden shadow-md aspect-square bg-white">
              <FallbackImage
                src={business.logo_url || business.image || business.coverImage}
                alt={`${business.name} logo`}
                fallbackSrc="/placeholder.svg"
                className="w-full h-full object-contain p-4"
              />
            </div>
          </div>

          {/* Right Column - Business Details */}
          <div className="w-full md:w-2/3 flex flex-col justify-between">
            {/* Business Name and Type */}
            <div>
              <div className="flex flex-wrap items-center justify-between mb-2">
                <div>
                  <h1 className="text-2xl font-bold">{business.name}</h1>
                  {business.businessTypeName && (
                    <div className="mt-1">
                      <Badge variant="outline" className="bg-emerald-50 text-emerald-700 border-emerald-200">
                        {business.businessTypeName}
                      </Badge>
                    </div>
                  )}
                </div>

                {/* More Info and Map Buttons */}
                <div className="flex gap-2 mt-2 md:mt-0">
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex items-center gap-1 text-sm"
                    onClick={() => setShowInfoDialog(true)}
                  >
                    <Info size={14} />
                    More Info
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex items-center gap-1 text-sm"
                    onClick={() => setShowOSMMapDialog(true)}
                  >
                    <MapIcon size={14} />
                    View Map
                  </Button>
                </div>
              </div>

              {/* Rating and Location */}
              <div className="flex flex-wrap items-center gap-4 mt-3 text-sm">
                {business.rating && (
                  <button
                    onClick={scrollToReviews}
                    className="flex items-center hover:bg-gray-50 rounded-md px-2 py-1 -mx-2 -my-1 transition-colors cursor-pointer"
                    title="View customer reviews"
                  >
                    <Star className="h-4 w-4 text-yellow-400 fill-yellow-400 mr-1" />
                    <span className="font-medium">{business.rating.toFixed(1)}</span>
                    {business.reviewCount && (
                      <span className="text-gray-500 ml-1">({business.reviewCount})</span>
                    )}
                  </button>
                )}

                <div className="flex items-center">
                  <MapPin className="h-4 w-4 text-rose-400 mr-1" />
                  <span>{business.location}, Jersey</span>
                </div>

                {business.phone && (
                  <div className="flex items-center">
                    <ClickablePhone phoneNumber={business.phone} />
                  </div>
                )}
              </div>

              {/* Description */}
              {business.description && (
                <div className="mt-3">
                  <p className="text-sm text-gray-600 line-clamp-2">{business.description}</p>
                </div>
              )}
            </div>

            {/* Delivery Info Card - For errand businesses, don't show delivery options */}
            {business.businessType !== 'errand' && (
              /* For other business types, show pickup/delivery options */
              <div className="bg-white rounded-lg shadow-sm p-4 mt-4 border border-gray-100">
                <div className="mb-4">
                  <div className="flex items-center justify-center mb-4">
                    {business.deliveryAvailable ? (
                      deliveryMethod === 'delivery' ? (
                        <h3 className="text-lg font-medium text-gray-900">Delivery selected</h3>
                      ) : (
                        <h3 className="text-lg font-medium text-gray-900">Pickup selected</h3>
                      )
                    ) : (
                      <div className="flex items-center">
                        <h3 className="text-lg font-medium text-gray-900">Pickup</h3>
                        <span className="ml-3 px-3 py-1 bg-orange-100 text-orange-800 text-sm font-medium rounded-full">
                          Only option
                        </span>
                      </div>
                    )}
                  </div>

                  {business.deliveryAvailable ? (
                    /* Both pickup and delivery available - always show both cards */
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {/* Pickup Option */}
                      <div
                        className={`cursor-pointer rounded-lg border p-6 transition-all duration-200 ${
                          deliveryMethod === 'pickup'
                            ? 'border-orange-400 bg-orange-50 shadow-md'
                            : 'border-gray-200 bg-white hover:border-orange-300'
                        }`}
                        onClick={() => handleDeliveryMethodChange('pickup')}
                      >
                        <div className="flex items-center justify-center mb-4">
                          <Store className={`h-6 w-6 mr-2 ${
                            deliveryMethod === 'pickup' ? 'text-orange-600' : 'text-gray-600'
                          }`} />
                          <span className={`text-xl font-semibold ${
                            deliveryMethod === 'pickup' ? 'text-orange-900' : 'text-gray-900'
                          }`}>Pickup</span>
                        </div>
                        <div className="space-y-3">
                          <div className="flex justify-between items-center">
                            <span className={`text-sm ${
                              deliveryMethod === 'pickup' ? 'text-orange-600' : 'text-gray-600'
                            }`}>Ready in</span>
                            <span className={`font-medium ${
                              deliveryMethod === 'pickup' ? 'text-orange-900' : 'text-gray-900'
                            }`}>{prepTime || 15} min</span>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className={`text-sm ${
                              deliveryMethod === 'pickup' ? 'text-orange-600' : 'text-gray-600'
                            }`}>Fee</span>
                            <span className={`font-medium ${
                              deliveryMethod === 'pickup' ? 'text-orange-900' : 'text-gray-900'
                            }`}>Free</span>
                          </div>
                        </div>
                      </div>

                      {/* Delivery Option */}
                      <div
                        className={`cursor-pointer rounded-lg border p-6 transition-all duration-200 ${
                          deliveryMethod === 'delivery'
                            ? 'border-blue-400 bg-blue-50 shadow-md'
                            : 'border-gray-200 bg-white hover:border-blue-300'
                        }`}
                        onClick={() => handleDeliveryMethodChange('delivery')}
                      >
                        <div className="flex items-center justify-center mb-4">
                          <Truck className={`h-6 w-6 mr-2 ${
                            deliveryMethod === 'delivery' ? 'text-blue-600' : 'text-gray-600'
                          }`} />
                          <span className={`text-xl font-semibold ${
                            deliveryMethod === 'delivery' ? 'text-blue-900' : 'text-gray-900'
                          }`}>Delivery</span>
                        </div>
                        <div className="space-y-3">
                          <div className="flex justify-between items-center">
                            <span className={`text-sm ${
                              deliveryMethod === 'delivery' ? 'text-blue-600' : 'text-gray-600'
                            }`}>Fee</span>
                            <span className={`font-medium ${
                              deliveryMethod === 'delivery' ? 'text-blue-900' : 'text-gray-900'
                            }`}>{deliveryData.fee}</span>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className={`text-sm ${
                              deliveryMethod === 'delivery' ? 'text-blue-600' : 'text-gray-600'
                            }`}>Time</span>
                            <span className={`font-medium ${
                              deliveryMethod === 'delivery' ? 'text-blue-900' : 'text-gray-900'
                            }`}>{deliveryData.timeRange}</span>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className={`text-sm ${
                              deliveryMethod === 'delivery' ? 'text-blue-600' : 'text-gray-600'
                            }`}>Distance</span>
                            <span className={`font-medium ${
                              deliveryMethod === 'delivery' ? 'text-blue-900' : 'text-gray-900'
                            }`}>
                              {deliveryData.distanceKm ? `${deliveryData.distanceKm.toFixed(1)} km` : '0.8 km'}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ) : (
                    /* Only pickup available - show pickup card and map */
                    <div className="grid grid-cols-2 gap-4">
                      {/* Pickup Option */}
                      <div className="cursor-pointer rounded-lg border border-orange-400 bg-orange-50 shadow-md p-6 transition-all duration-200">
                        <div className="flex items-center justify-center mb-4">
                          <Store className="h-6 w-6 mr-2 text-orange-600" />
                          <span className="text-xl font-semibold text-orange-900">Pickup</span>
                        </div>
                        <div className="space-y-3">
                          <div className="flex justify-between items-center">
                            <span className="text-sm text-orange-600">Ready in</span>
                            <span className="font-medium text-orange-900">{prepTime || 15} min</span>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="text-sm text-orange-600">Fee</span>
                            <span className="font-medium text-orange-900">Free</span>
                          </div>
                        </div>
                      </div>

                      {/* Pickup Location Map */}
                      <div className="rounded-lg border border-gray-200 bg-white p-4">
                        <div className="flex items-center justify-center mb-3">
                          <MapIcon className="h-5 w-5 mr-2 text-orange-600" />
                          <span className="text-lg font-semibold text-gray-900">Pickup Location</span>
                        </div>
                        <div className="h-32 rounded-lg overflow-hidden">
                          <BusinessLocationMap
                            businessName={business.name}
                            businessLocation={business.location}
                            coordinates={coordinates}
                            height="128px"
                            interactive={false}
                          />
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Search and Categories Section */}
      <div className="sticky top-[76px] z-30 bg-white shadow-sm border-b border-gray-100">
        <div className="container-fluid pt-4 pb-2">
          {/* Search Input */}
          <div className="relative mb-2">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search className="h-4 w-4 text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Search menu items..."
              value={searchQuery}
              onChange={handleSearchChange}
              className="w-full pl-10 pr-10 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-emerald-500 focus:border-transparent"
            />
            {searchQuery && (
              <button
                onClick={() => setSearchQuery("")}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
              >
                ×
              </button>
            )}
          </div>

          {/* Category Scroller - Hide when searching */}
          {!searchQuery && business.menuCategories && business.menuCategories.length > 0 && (
            <div>
              <CategoryScroller
                categories={business.menuCategories}
                activeCategory={activeCategory}
                onCategoryChange={handleCategoryChange}
              />
            </div>
          )}
        </div>
      </div>

      {/* Mobile Order Section - Only visible on small screens */}
      <div className="lg:hidden container-fluid py-3 mb-4">
        <div className="bg-white rounded-lg shadow-md p-4 border border-gray-100">
          <div className="flex items-center mb-2">
            <ShoppingBag className="mr-2 text-emerald-600" />
            <h3 className="text-lg font-semibold">Your Order with {business.name}</h3>
          </div>

          {businessItems.length === 0 ? (
            <div className="border-t border-b py-3 my-3">
              <p className="text-center text-gray-500 text-sm">Add items from this business to your order</p>
            </div>
          ) : (
            <div className="border-t pt-3 my-3">
              <div className="max-h-40 overflow-y-auto mb-3">
                {businessItems.map((item) => (
                  <div key={item.cartItemId || `${item.id}-${Math.random()}`} className="flex justify-between mb-2">
                    <div>
                      <p className="font-medium text-sm">
                        {item.quantity}x {item.name}
                      </p>
                      {(() => {
                        const details = formatItemDetails(item);
                        return details.length > 0 && (
                          <p className="text-xs text-gray-500">{details.join(", ")}</p>
                        );
                      })()}
                    </div>
                    <p className="font-medium text-sm">£{(item.price * item.quantity).toFixed(2)}</p>
                  </div>
                ))}
              </div>
              <div className="flex justify-between text-sm mt-2">
                <span>Subtotal</span>
                <span>£{businessSubtotal.toFixed(2)}</span>
              </div>
              {businessItems.length > 0 && (
                <>
                  <div className="flex justify-between text-sm mt-1">
                    <span>Delivery Fee</span>
                    <span>{deliveryData.fee}</span>
                  </div>
                  <div className="flex justify-between text-sm mt-1">
                    <span>Service Fee</span>
                    <span>£0.50</span>
                  </div>
                </>
              )}
              <div className="flex justify-between font-semibold pt-2 border-t text-sm">
                <span>{business.name} Total</span>
                <span>£{(businessSubtotal + (businessItems.length > 0 ? deliveryData.feeNumeric + 0.5 : 0)).toFixed(2)}</span>
              </div>
              <CheckoutLink
                className="block mt-3"
                buttonClassName={`w-full text-sm py-1 ${
                  business.businessType === 'errand'
                    ? 'bg-green-600 hover:bg-green-700'
                    : !business.deliveryAvailable || deliveryMethod === 'pickup'
                    ? 'bg-orange-600 hover:bg-orange-700'
                    : 'bg-blue-600 hover:bg-blue-700'
                }`}
              >
                Go to checkout
              </CheckoutLink>
            </div>
          )}
        </div>
      </div>

      {/* Main Content Area with Menu and Order - Fixed Layout */}
      <div className="relative">
        <div className="container-fluid py-3">
          <div className="flex flex-col lg:flex-row lg:items-start lg:gap-6">
            {/* Left Column - Menu Content */}
            <div className="w-full lg:w-[calc(100%-340px)]">
              {/* Discount Card */}
              <Card className="mb-4 bg-emerald-50 border-emerald-200">
                <CardContent className="p-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-start gap-2">
                      <div className="bg-emerald-100 p-1.5 rounded-full">
                        <UtensilsCrossed className="h-4 w-4 text-emerald-600" />
                      </div>
                      <div>
                        <h3 className="font-medium text-emerald-800 text-sm">First-time customer?</h3>
                        <p className="text-xs text-emerald-700">
                          Use code <span className="font-bold">{discountCode}</span> for 25% off your first order
                        </p>
                      </div>
                    </div>
                    <CopyButton
                      value={discountCode}
                      tooltipText="Copy discount code"
                      successText="Code copied!"
                      variant="outline"
                      size="sm"
                      className="border-emerald-200 hover:bg-emerald-100"
                    />
                  </div>
                </CardContent>
              </Card>

              {/* Menu Categories or Search Results */}
              {filteredMenuItems && filteredMenuItems.length > 0 ? (
                <div>
                  <h2 className="text-lg font-bold mb-3">Search Results</h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3 auto-rows-fr mb-6">
                    {filteredMenuItems.map((item) => (
                      <ProductItem
                        key={item.id}
                        product={item}
                        businessId={business.id}
                        businessSlug={business.slug}
                        businessName={business.name}
                        businessType={business.businessType || 'restaurant'}
                        categoryId={item.categoryId}
                        layout={business.slimMenu ? "slim" : "compact"}
                        isTemporarilyClosed={businessClosure?.isTemporarilyClosed || false}
                        closureMessage={businessClosure?.closureMessage}
                        openingHours={business.opening_hours}
                        pickupAvailable={business.pickup_available}
                        deliveryAvailable={business.delivery_available}
                      />
                    ))}
                  </div>
                </div>
              ) : filteredMenuItems && filteredMenuItems.length === 0 ? (
                <div className="p-4 text-center bg-white rounded-lg border border-gray-100 shadow-sm">
                  <p className="text-gray-600">No menu items found matching "{searchQuery}"</p>
                </div>
              ) : business.menuCategories && business.menuCategories.length > 0 ? (
                <div>
                  {business.menuCategories.map((category) => (
                    <div
                      key={category.id}
                      id={`category-${category.id}`}
                      className="scroll-mt-[200px] mb-6"
                    >
                      <h2 className="text-lg font-bold mb-3">{category.name}</h2>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3 auto-rows-fr">
                        {category.items.map((item) => (
                          <ProductItem
                            key={item.id}
                            product={item}
                            businessId={business.id}
                            businessSlug={business.slug}
                            businessName={business.name}
                            businessType={business.businessType || 'restaurant'}
                            categoryId={category.id}
                            layout={business.slimMenu ? "slim" : "compact"}
                            isTemporarilyClosed={businessClosure?.isTemporarilyClosed || false}
                            closureMessage={businessClosure?.closureMessage}
                            openingHours={business.opening_hours}
                            pickupAvailable={business.pickup_available}
                            deliveryAvailable={business.delivery_available}
                          />
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="p-8 text-center bg-white rounded-lg border border-gray-100 shadow-sm">
                  <UtensilsCrossed className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No menu available</h3>
                  <p className="text-gray-600">This business hasn't added their menu yet.</p>
                </div>
              )}

              {/* Customer Reviews Section */}
              <div id="customer-reviews" className="mt-8">
                <Card className="bg-white border border-gray-100 shadow-sm">
                  <CardContent className="p-6">
                    <h2 className="text-xl font-bold mb-4 flex items-center gap-2">
                      <Star className="h-5 w-5 text-yellow-400" />
                      Customer Reviews
                    </h2>
                    <ReviewDisplay
                      businessId={business.id}
                      businessName={business.name}
                      showHeader={false}
                      limit={5}
                    />
                  </CardContent>
                </Card>
              </div>
            </div>

            {/* Right Column - Sticky Order Section */}
            <div className="hidden lg:block sticky top-[200px] w-[320px] z-20 self-start ml-auto mb-8">
              <div className="bg-white rounded-lg shadow-md p-6 border border-gray-100 max-h-[calc(100vh-200px)] overflow-y-auto">
                <div className="flex items-center mb-4">
                  {getBusinessTypeIconWithStyle(business.businessType || business.business_types?.slug, "mr-2 text-emerald-600")}
                  <h3 className="text-lg font-semibold">Your Order with {business.name}</h3>
                </div>

                {businessItems.length === 0 ? (
                  <div className="border-t border-b py-4 my-4">
                    <p className="text-center text-gray-500">Add items from this business to your order</p>
                    <p className="text-center text-sm text-emerald-600 mt-2">
                      You can order from multiple businesses at once!
                    </p>
                  </div>
                ) : (
                  <div className="border-t pt-4 my-4">
                    <div className="max-h-60 overflow-y-auto mb-4">
                      {businessItems.map((item) => (
                        <div key={item.cartItemId || `${item.id}-${Math.random()}`} className="flex justify-between mb-3">
                          <div>
                            <p className="font-medium">
                              {item.quantity}x {item.name}
                            </p>
                            {(() => {
                              const details = formatItemDetails(item);
                              return details.length > 0 && (
                                <p className="text-sm text-gray-500">{details.join(", ")}</p>
                              );
                            })()}
                          </div>
                          <p className="font-medium">£{(item.price * item.quantity).toFixed(2)}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                <div className="space-y-4">
                  <div className="flex justify-between text-sm">
                    <span>Subtotal</span>
                    <span>£{businessSubtotal.toFixed(2)}</span>
                  </div>
                  {businessItems.length > 0 && (
                    <>
                      <div className="flex justify-between text-sm">
                        <span>Delivery Fee</span>
                        <span>{deliveryData.fee}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Service Fee</span>
                        <span>£0.50</span>
                      </div>
                    </>
                  )}
                  <div className="flex justify-between font-semibold pt-4 border-t">
                    <span>{business.name} Total</span>
                    <span>£{(businessSubtotal + (businessItems.length > 0 ? deliveryData.feeNumeric + 0.5 : 0)).toFixed(2)}</span>
                  </div>
                </div>

                {businessItems.length > 0 ? (
                  <CheckoutLink
                    buttonClassName={`w-full mt-6 ${
                      business.businessType === 'errand'
                        ? 'bg-green-600 hover:bg-green-700'
                        : !business.deliveryAvailable || deliveryMethod === 'pickup'
                        ? 'bg-orange-600 hover:bg-orange-700'
                        : 'bg-blue-600 hover:bg-blue-700'
                    }`}
                  >
                    Go to checkout
                  </CheckoutLink>
                ) : (
                  <Button className={`w-full mt-6 ${
                    business.businessType === 'errand'
                      ? 'bg-green-600 hover:bg-green-700'
                      : !business.deliveryAvailable || deliveryMethod === 'pickup'
                      ? 'bg-orange-600 hover:bg-orange-700'
                      : 'bg-blue-600 hover:bg-blue-700'
                  }`} disabled>
                    Go to checkout
                  </Button>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
      </div> {/* Close main content div */}

      {/* PHASE 4 STEP 9: Business Closed Dialog */}
      <BusinessClosedDialog
        isOpen={showClosedDialog}
        onClose={() => setShowClosedDialog(false)}
        businessName={business.name}
        closureMessage={businessClosure?.closureMessage || ""}
      />

      {/* Business Info Dialog */}
      <BusinessInfoDialog
        business={business}
        open={showInfoDialog}
        onOpenChange={setShowInfoDialog}
      />

      {/* Business Map Dialog */}
      <Dialog open={showOSMMapDialog} onOpenChange={setShowOSMMapDialog}>
        <DialogContent className="sm:max-w-3xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <MapIcon size={18} className="text-emerald-600" />
              {business.name} Delivery Map
            </DialogTitle>
          </DialogHeader>
          <div className="py-2">
            <OSMRestaurantMap
              restaurantId={business.id}
              name={business.name}
              location={business.location}
              coordinates={coordinates}
              deliveryTime={business.deliveryTime || 30}
              deliveryRadius={deliveryRadius}
            />
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
