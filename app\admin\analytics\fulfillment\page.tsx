'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Clock, Timer, TrendingUp, TrendingDown, Target,
  CheckCircle, AlertTriangle, XCircle, Package,
  Truck, MapPin, Activity, BarChart3, Zap
} from 'lucide-react'

interface FulfillmentAnalytics {
  // Overall Performance
  totalOrders: number
  avgPrepTime: number
  avgDeliveryTime: number
  avgDriverResponseTime: number
  avgPickupTime: number
  avgTotalFulfillmentTime: number

  // Performance Categories
  earlyOrders: number
  onTimeOrders: number
  delayedOrders: number
  
  // Time Variance Analysis
  avgPrepVariance: number // positive = late, negative = early
  avgDeliveryVariance: number
  avgTotalVariance: number
  
  // Driver Performance
  fastDriverResponses: number // < 2 minutes
  normalDriverResponses: number // 2-10 minutes
  slowDriverResponses: number // > 10 minutes
  
  // Pickup Performance
  quickPickups: number // < 5 minutes after assignment
  normalPickups: number // 5-15 minutes
  delayedPickups: number // > 15 minutes
  
  // Business Performance
  businessesAheadOfSchedule: number
  businessesOnSchedule: number
  businessesBehindSchedule: number
  
  // Issue Analysis
  weatherDelays: number
  trafficDelays: number
  equipmentIssues: number
  navigationIssues: number
  businessDelays: number
  
  // Recent Orders Detail
  recentOrders: Array<{
    id: number
    order_number: string
    business_name: string
    driver_name: string
    delivery_parish: string
    estimated_prep: number
    actual_prep: number
    estimated_delivery: number
    actual_delivery: number
    driver_response_seconds: number
    pickup_delay_minutes: number
    status: string
    created_at: string
    delivered_at: string
    performance_category: 'early' | 'ontime' | 'delayed'
  }>
}

export default function FulfillmentAnalyticsPage() {
  const [analytics, setAnalytics] = useState<FulfillmentAnalytics | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchAnalytics = async () => {
      try {
        const response = await fetch('/api/admin/analytics/fulfillment')

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        const result = await response.json()

        if (result.success) {
          setAnalytics(result.data)
        } else {
          throw new Error(result.error || 'Failed to fetch analytics')
        }
      } catch (err) {
        console.error('Error fetching fulfillment analytics:', err)
        setError(err instanceof Error ? err.message : 'Failed to load analytics')
      } finally {
        setIsLoading(false)
      }
    }

    fetchAnalytics()
  }, [])

  if (isLoading) {
    return (
      <div className="container mx-auto p-6">
        <h1 className="text-3xl font-bold mb-6">Order Fulfillment Analytics</h1>
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-400"></div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="container mx-auto p-6">
        <h1 className="text-3xl font-bold mb-6">Order Fulfillment Analytics</h1>
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-600">Error: {error}</p>
        </div>
      </div>
    )
  }

  // Helper functions for formatting
  const formatTime = (minutes: number) => {
    if (minutes < 1) return `${Math.round(minutes * 60)}s`
    return `${Math.round(minutes)}m`
  }
  
  const formatVariance = (variance: number) => {
    const absVariance = Math.abs(variance)
    const sign = variance >= 0 ? '+' : '-'
    return `${sign}${formatTime(absVariance)}`
  }
  
  const getVarianceColor = (variance: number) => {
    if (variance < -2) return 'text-green-600' // Early
    if (variance > 5) return 'text-red-600' // Late
    return 'text-yellow-600' // On time
  }
  
  const getPerformanceIcon = (category: string) => {
    switch (category) {
      case 'early': return <TrendingUp className="h-4 w-4 text-green-600" />
      case 'ontime': return <Target className="h-4 w-4 text-blue-600" />
      case 'delayed': return <TrendingDown className="h-4 w-4 text-red-600" />
      default: return <Clock className="h-4 w-4" />
    }
  }

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  return (
    <div className="container mx-auto p-6 space-y-8">
      <h1 className="text-3xl font-bold mb-6">Order Fulfillment Analytics</h1>

      {/* Overall Performance Overview */}
      <div>
        <h2 className="text-xl font-semibold mb-4 flex items-center">
          <Activity className="h-5 w-5 mr-2" />
          Overall Performance
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Orders</CardTitle>
              <Package className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{analytics?.totalOrders || 0}</div>
              <p className="text-xs text-muted-foreground">Analyzed orders</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg Prep Time</CardTitle>
              <Timer className="h-4 w-4 text-orange-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatTime(analytics?.avgPrepTime || 0)}</div>
              <p className="text-xs text-muted-foreground">Kitchen to ready</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg Delivery</CardTitle>
              <Truck className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatTime(analytics?.avgDeliveryTime || 0)}</div>
              <p className="text-xs text-muted-foreground">Pickup to delivery</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Driver Response</CardTitle>
              <Zap className="h-4 w-4 text-purple-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatTime(analytics?.avgDriverResponseTime || 0)}</div>
              <p className="text-xs text-muted-foreground">Offer to accept</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pickup Time</CardTitle>
              <MapPin className="h-4 w-4 text-indigo-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatTime(analytics?.avgPickupTime || 0)}</div>
              <p className="text-xs text-muted-foreground">Assign to pickup</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Time</CardTitle>
              <Clock className="h-4 w-4 text-slate-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatTime(analytics?.avgTotalFulfillmentTime || 0)}</div>
              <p className="text-xs text-muted-foreground">Order to delivery</p>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Performance Categories */}
      <div>
        <h2 className="text-xl font-semibold mb-4 flex items-center">
          <Target className="h-5 w-5 mr-2" />
          Performance Categories
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Early Deliveries</CardTitle>
              <TrendingUp className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{analytics?.earlyOrders || 0}</div>
              <p className="text-xs text-muted-foreground">
                {analytics?.totalOrders ? Math.round((analytics.earlyOrders / analytics.totalOrders) * 100) : 0}% of total orders
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">On-Time Deliveries</CardTitle>
              <Target className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">{analytics?.onTimeOrders || 0}</div>
              <p className="text-xs text-muted-foreground">
                {analytics?.totalOrders ? Math.round((analytics.onTimeOrders / analytics.totalOrders) * 100) : 0}% of total orders
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Delayed Deliveries</CardTitle>
              <TrendingDown className="h-4 w-4 text-red-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{analytics?.delayedOrders || 0}</div>
              <p className="text-xs text-muted-foreground">
                {analytics?.totalOrders ? Math.round((analytics.delayedOrders / analytics.totalOrders) * 100) : 0}% of total orders
              </p>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Time Variance Analysis */}
      <div>
        <h2 className="text-xl font-semibold mb-4 flex items-center">
          <BarChart3 className="h-5 w-5 mr-2" />
          Time Variance Analysis
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Prep Time Variance</CardTitle>
              <Timer className="h-4 w-4 text-orange-600" />
            </CardHeader>
            <CardContent>
              <div className={`text-2xl font-bold ${getVarianceColor(analytics?.avgPrepVariance || 0)}`}>
                {formatVariance(analytics?.avgPrepVariance || 0)}
              </div>
              <p className="text-xs text-muted-foreground">vs estimated prep time</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Delivery Time Variance</CardTitle>
              <Truck className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className={`text-2xl font-bold ${getVarianceColor(analytics?.avgDeliveryVariance || 0)}`}>
                {formatVariance(analytics?.avgDeliveryVariance || 0)}
              </div>
              <p className="text-xs text-muted-foreground">vs estimated delivery time</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Time Variance</CardTitle>
              <Clock className="h-4 w-4 text-slate-600" />
            </CardHeader>
            <CardContent>
              <div className={`text-2xl font-bold ${getVarianceColor(analytics?.avgTotalVariance || 0)}`}>
                {formatVariance(analytics?.avgTotalVariance || 0)}
              </div>
              <p className="text-xs text-muted-foreground">vs total estimated time</p>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Driver Performance */}
      <div>
        <h2 className="text-xl font-semibold mb-4 flex items-center">
          <Zap className="h-5 w-5 mr-2" />
          Driver Response Performance
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Fast Responses</CardTitle>
              <CheckCircle className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{analytics?.fastDriverResponses || 0}</div>
              <p className="text-xs text-muted-foreground">Under 2 minutes</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Normal Responses</CardTitle>
              <Timer className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">{analytics?.normalDriverResponses || 0}</div>
              <p className="text-xs text-muted-foreground">2-10 minutes</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Slow Responses</CardTitle>
              <AlertTriangle className="h-4 w-4 text-red-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{analytics?.slowDriverResponses || 0}</div>
              <p className="text-xs text-muted-foreground">Over 10 minutes</p>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Pickup Performance */}
      <div>
        <h2 className="text-xl font-semibold mb-4 flex items-center">
          <MapPin className="h-5 w-5 mr-2" />
          Pickup Performance
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Quick Pickups</CardTitle>
              <CheckCircle className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{analytics?.quickPickups || 0}</div>
              <p className="text-xs text-muted-foreground">Under 5 minutes</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Normal Pickups</CardTitle>
              <Timer className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">{analytics?.normalPickups || 0}</div>
              <p className="text-xs text-muted-foreground">5-15 minutes</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Delayed Pickups</CardTitle>
              <XCircle className="h-4 w-4 text-red-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{analytics?.delayedPickups || 0}</div>
              <p className="text-xs text-muted-foreground">Over 15 minutes</p>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Issue Analysis */}
      <div>
        <h2 className="text-xl font-semibold mb-4 flex items-center">
          <AlertTriangle className="h-5 w-5 mr-2" />
          Common Issues
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Weather Delays</CardTitle>
              <AlertTriangle className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{analytics?.weatherDelays || 0}</div>
              <p className="text-xs text-muted-foreground">Rain, wind, etc.</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Traffic Delays</CardTitle>
              <AlertTriangle className="h-4 w-4 text-red-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{analytics?.trafficDelays || 0}</div>
              <p className="text-xs text-muted-foreground">Congestion, accidents</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Equipment Issues</CardTitle>
              <AlertTriangle className="h-4 w-4 text-orange-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{analytics?.equipmentIssues || 0}</div>
              <p className="text-xs text-muted-foreground">Vehicle, kitchen problems</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Navigation Issues</CardTitle>
              <AlertTriangle className="h-4 w-4 text-purple-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{analytics?.navigationIssues || 0}</div>
              <p className="text-xs text-muted-foreground">GPS, address problems</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Business Delays</CardTitle>
              <AlertTriangle className="h-4 w-4 text-yellow-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{analytics?.businessDelays || 0}</div>
              <p className="text-xs text-muted-foreground">Kitchen overwhelmed</p>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Recent Orders Detail */}
      {analytics?.recentOrders && analytics.recentOrders.length > 0 && (
        <div>
          <h2 className="text-xl font-semibold mb-4 flex items-center">
            <Package className="h-5 w-5 mr-2" />
            Recent Order Performance
          </h2>
          <Card>
            <CardContent className="p-0">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Order
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Business
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Driver
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Parish
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Prep Time
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Delivery Time
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Driver Response
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Performance
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Completed
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {analytics.recentOrders.map((order) => (
                      <tr key={order.id} className="hover:bg-gray-50">
                        <td className="px-4 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {order.order_number}
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                          {order.business_name}
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                          {order.driver_name}
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                          {order.delivery_parish}
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                          <div className="flex flex-col">
                            <span>{order.actual_prep}m</span>
                            <span className="text-xs text-gray-400">
                              est: {order.estimated_prep}m
                            </span>
                          </div>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                          <div className="flex flex-col">
                            <span>{order.actual_delivery}m</span>
                            <span className="text-xs text-gray-400">
                              est: {order.estimated_delivery}m
                            </span>
                          </div>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                          <div className="flex flex-col">
                            <span>{formatTime(order.driver_response_seconds / 60)}</span>
                            <span className="text-xs text-gray-400">
                              pickup: {order.pickup_delay_minutes}m
                            </span>
                          </div>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            {getPerformanceIcon(order.performance_category)}
                            <span className={`ml-2 text-sm capitalize ${
                              order.performance_category === 'early' ? 'text-green-600' :
                              order.performance_category === 'ontime' ? 'text-blue-600' :
                              'text-red-600'
                            }`}>
                              {order.performance_category}
                            </span>
                          </div>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                          {formatDateTime(order.delivered_at)}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}
