-- Create audit trail tables for immutable order tracking
-- This ensures we never overwrite historical data and can track all changes

-- 1. Order Time Estimates Table
-- Tracks all time estimate changes without overwriting original estimates
CREATE TABLE order_time_estimates (
  id SERIAL PRIMARY KEY,
  order_id INTEGER NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
  business_id INTEGER REFERENCES businesses(id), -- Which business updated the time (NULL for system/driver updates)
  
  -- Type of estimate being recorded
  estimate_type VARCHAR(30) NOT NULL CHECK (estimate_type IN (
    'initial_prep', 'updated_prep', 'initial_delivery', 'updated_delivery',
    'initial_pickup', 'updated_pickup', 'driver_eta', 'system_recalc'
  )),
  
  -- Time estimates
  estimated_minutes INTEGER, -- Duration in minutes (for prep time, travel time, etc.)
  estimated_ready_time TIMESTAMP WITH TIME ZONE, -- When order will be ready
  estimated_delivery_time TIMESTAMP WITH TIME ZONE, -- When order will be delivered
  estimated_pickup_time TIMESTAMP WITH TIME ZONE, -- When driver will pickup
  
  -- Context and reasoning
  reason VARCHAR(100), -- 'business_update', 'driver_delay', 'traffic', 'kitchen_busy', etc.
  notes TEXT, -- Additional details about the estimate change
  
  -- Audit trail
  created_by_user_id INTEGER REFERENCES users(id),
  created_by_role VARCHAR(20) CHECK (created_by_role IN ('business', 'driver', 'system', 'customer')),
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Order Delivery Updates Table  
-- Tracks delivery progress, driver location, delays, and issues
CREATE TABLE order_delivery_updates (
  id SERIAL PRIMARY KEY,
  order_id INTEGER NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
  driver_id INTEGER REFERENCES users(id), -- Driver making the update (NULL for system updates)
  
  -- Type of update
  update_type VARCHAR(30) NOT NULL CHECK (update_type IN (
    'location', 'delay', 'issue', 'eta_update', 'pickup_arrived', 
    'pickup_completed', 'delivery_arrived', 'delivery_attempted', 'delivery_completed'
  )),
  
  -- Location data
  driver_location_lat DECIMAL(10, 8),
  driver_location_lng DECIMAL(11, 8),
  location_accuracy_meters INTEGER, -- GPS accuracy
  
  -- Time estimates and delays
  estimated_arrival_time TIMESTAMP WITH TIME ZONE,
  delay_minutes INTEGER, -- How many minutes delayed from original estimate
  
  -- Issue tracking
  issue_type VARCHAR(50), -- 'traffic', 'weather', 'vehicle_issue', 'customer_unavailable', etc.
  resolution_status VARCHAR(20) CHECK (resolution_status IN ('pending', 'resolved', 'escalated')),
  
  -- Additional context
  notes TEXT,
  photo_url TEXT, -- For delivery proof, issues, etc.
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Create indexes for performance
CREATE INDEX idx_order_time_estimates_order_id ON order_time_estimates(order_id);
CREATE INDEX idx_order_time_estimates_business_id ON order_time_estimates(business_id);
CREATE INDEX idx_order_time_estimates_type ON order_time_estimates(estimate_type);
CREATE INDEX idx_order_time_estimates_created_at ON order_time_estimates(created_at);

CREATE INDEX idx_order_delivery_updates_order_id ON order_delivery_updates(order_id);
CREATE INDEX idx_order_delivery_updates_driver_id ON order_delivery_updates(driver_id);
CREATE INDEX idx_order_delivery_updates_type ON order_delivery_updates(update_type);
CREATE INDEX idx_order_delivery_updates_created_at ON order_delivery_updates(created_at);
CREATE INDEX idx_order_delivery_updates_location ON order_delivery_updates(driver_location_lat, driver_location_lng);

-- 4. Add helpful comments
COMMENT ON TABLE order_time_estimates IS 'Immutable log of all time estimate changes - never overwrite, always append';
COMMENT ON TABLE order_delivery_updates IS 'Immutable log of delivery progress updates including location and issues';

COMMENT ON COLUMN order_time_estimates.estimate_type IS 'Type of estimate: initial_prep, updated_prep, initial_delivery, updated_delivery, etc.';
COMMENT ON COLUMN order_time_estimates.reason IS 'Why the estimate changed: business_update, driver_delay, traffic, kitchen_busy, etc.';

COMMENT ON COLUMN order_delivery_updates.update_type IS 'Type of update: location, delay, issue, eta_update, pickup_arrived, etc.';
COMMENT ON COLUMN order_delivery_updates.issue_type IS 'Type of issue: traffic, weather, vehicle_issue, customer_unavailable, etc.';

-- 5. Helper functions for recording time estimates
CREATE OR REPLACE FUNCTION record_time_estimate(
  p_order_id INTEGER,
  p_business_id INTEGER DEFAULT NULL,
  p_estimate_type VARCHAR(30),
  p_estimated_minutes INTEGER DEFAULT NULL,
  p_estimated_ready_time TIMESTAMP WITH TIME ZONE DEFAULT NULL,
  p_estimated_delivery_time TIMESTAMP WITH TIME ZONE DEFAULT NULL,
  p_estimated_pickup_time TIMESTAMP WITH TIME ZONE DEFAULT NULL,
  p_reason VARCHAR(100) DEFAULT NULL,
  p_notes TEXT DEFAULT NULL,
  p_created_by_user_id INTEGER DEFAULT NULL,
  p_created_by_role VARCHAR(20) DEFAULT 'system'
) RETURNS INTEGER AS $$
DECLARE
  v_estimate_id INTEGER;
BEGIN
  -- Insert the time estimate record
  INSERT INTO order_time_estimates (
    order_id, business_id, estimate_type, estimated_minutes,
    estimated_ready_time, estimated_delivery_time, estimated_pickup_time,
    reason, notes, created_by_user_id, created_by_role
  ) VALUES (
    p_order_id, p_business_id, p_estimate_type, p_estimated_minutes,
    p_estimated_ready_time, p_estimated_delivery_time, p_estimated_pickup_time,
    p_reason, p_notes, p_created_by_user_id, p_created_by_role
  ) RETURNING id INTO v_estimate_id;

  RETURN v_estimate_id;
END;
$$ LANGUAGE plpgsql;

-- 6. Helper function for recording delivery updates
CREATE OR REPLACE FUNCTION record_delivery_update(
  p_order_id INTEGER,
  p_driver_id INTEGER DEFAULT NULL,
  p_update_type VARCHAR(30),
  p_driver_location_lat DECIMAL(10, 8) DEFAULT NULL,
  p_driver_location_lng DECIMAL(11, 8) DEFAULT NULL,
  p_location_accuracy_meters INTEGER DEFAULT NULL,
  p_estimated_arrival_time TIMESTAMP WITH TIME ZONE DEFAULT NULL,
  p_delay_minutes INTEGER DEFAULT NULL,
  p_issue_type VARCHAR(50) DEFAULT NULL,
  p_resolution_status VARCHAR(20) DEFAULT NULL,
  p_notes TEXT DEFAULT NULL,
  p_photo_url TEXT DEFAULT NULL
) RETURNS INTEGER AS $$
DECLARE
  v_update_id INTEGER;
BEGIN
  -- Insert the delivery update record
  INSERT INTO order_delivery_updates (
    order_id, driver_id, update_type, driver_location_lat, driver_location_lng,
    location_accuracy_meters, estimated_arrival_time, delay_minutes,
    issue_type, resolution_status, notes, photo_url
  ) VALUES (
    p_order_id, p_driver_id, p_update_type, p_driver_location_lat, p_driver_location_lng,
    p_location_accuracy_meters, p_estimated_arrival_time, p_delay_minutes,
    p_issue_type, p_resolution_status, p_notes, p_photo_url
  ) RETURNING id INTO v_update_id;

  RETURN v_update_id;
END;
$$ LANGUAGE plpgsql;

-- 7. Create views for easy querying of audit trail data

-- View to get latest time estimates for each order
CREATE OR REPLACE VIEW order_current_estimates AS
SELECT DISTINCT ON (order_id, estimate_type)
  order_id,
  estimate_type,
  estimated_minutes,
  estimated_ready_time,
  estimated_delivery_time,
  estimated_pickup_time,
  reason,
  created_by_role,
  created_at
FROM order_time_estimates
ORDER BY order_id, estimate_type, created_at DESC;

-- View to get delivery progress timeline for orders
CREATE OR REPLACE VIEW order_delivery_timeline AS
SELECT
  odu.order_id,
  odu.update_type,
  odu.driver_location_lat,
  odu.driver_location_lng,
  odu.estimated_arrival_time,
  odu.delay_minutes,
  odu.issue_type,
  odu.resolution_status,
  odu.notes,
  odu.created_at,
  u.email as driver_email,
  dp.vehicle_type
FROM order_delivery_updates odu
LEFT JOIN users u ON odu.driver_id = u.id
LEFT JOIN driver_profiles dp ON u.id = dp.user_id
ORDER BY odu.order_id, odu.created_at;

-- View for comprehensive order audit trail
CREATE OR REPLACE VIEW order_audit_trail AS
SELECT
  'status_change' as event_type,
  osh.order_id,
  NULL as business_id,
  osh.status as event_data,
  osh.notes,
  osh.created_by as created_by_user_id,
  'system' as created_by_role,
  osh.created_at
FROM order_status_history osh

UNION ALL

SELECT
  'time_estimate' as event_type,
  ote.order_id,
  ote.business_id,
  ote.estimate_type as event_data,
  ote.notes,
  ote.created_by_user_id,
  ote.created_by_role,
  ote.created_at
FROM order_time_estimates ote

UNION ALL

SELECT
  'delivery_update' as event_type,
  odu.order_id,
  NULL as business_id,
  odu.update_type as event_data,
  odu.notes,
  odu.driver_id as created_by_user_id,
  'driver' as created_by_role,
  odu.created_at
FROM order_delivery_updates odu

ORDER BY order_id, created_at;

-- 8. Enable Row Level Security
ALTER TABLE order_time_estimates ENABLE ROW LEVEL SECURITY;
ALTER TABLE order_delivery_updates ENABLE ROW LEVEL SECURITY;

-- 9. Create RLS policies

-- Time estimates policies
CREATE POLICY "Users can view time estimates for their orders" ON order_time_estimates
  FOR SELECT USING (
    order_id IN (
      SELECT id FROM orders WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Businesses can view/insert time estimates for their orders" ON order_time_estimates
  FOR ALL USING (
    business_id IN (
      SELECT id FROM businesses WHERE user_id = auth.uid()
    )
    OR
    order_id IN (
      SELECT o.id FROM orders o
      JOIN order_items oi ON o.id = oi.order_id
      JOIN businesses b ON oi.business_id::text = b.id::text
      WHERE b.user_id = auth.uid()
    )
  );

CREATE POLICY "Drivers can view time estimates for assigned orders" ON order_time_estimates
  FOR SELECT USING (
    order_id IN (
      SELECT id FROM orders WHERE driver_id = auth.uid()
    )
  );

CREATE POLICY "System can manage all time estimates" ON order_time_estimates
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM users WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Delivery updates policies
CREATE POLICY "Users can view delivery updates for their orders" ON order_delivery_updates
  FOR SELECT USING (
    order_id IN (
      SELECT id FROM orders WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Drivers can manage delivery updates for their orders" ON order_delivery_updates
  FOR ALL USING (
    driver_id = auth.uid()
    OR
    order_id IN (
      SELECT id FROM orders WHERE driver_id = auth.uid()
    )
  );

CREATE POLICY "Businesses can view delivery updates for their orders" ON order_delivery_updates
  FOR SELECT USING (
    order_id IN (
      SELECT o.id FROM orders o
      JOIN order_items oi ON o.id = oi.order_id
      JOIN businesses b ON oi.business_id::text = b.id::text
      WHERE b.user_id = auth.uid()
    )
  );

CREATE POLICY "System can manage all delivery updates" ON order_delivery_updates
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM users WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- 10. Integration functions with existing notification system

-- Function to record time estimate change and trigger notifications
CREATE OR REPLACE FUNCTION update_order_time_estimate(
  p_order_id INTEGER,
  p_business_id INTEGER DEFAULT NULL,
  p_estimate_type VARCHAR(30),
  p_estimated_minutes INTEGER DEFAULT NULL,
  p_estimated_ready_time TIMESTAMP WITH TIME ZONE DEFAULT NULL,
  p_estimated_delivery_time TIMESTAMP WITH TIME ZONE DEFAULT NULL,
  p_reason VARCHAR(100) DEFAULT NULL,
  p_notes TEXT DEFAULT NULL,
  p_created_by_user_id INTEGER DEFAULT NULL,
  p_created_by_role VARCHAR(20) DEFAULT 'system',
  p_send_notification BOOLEAN DEFAULT TRUE
) RETURNS INTEGER AS $$
DECLARE
  v_estimate_id INTEGER;
  v_order_data RECORD;
  v_notification_title TEXT;
  v_notification_body TEXT;
BEGIN
  -- Record the time estimate
  SELECT record_time_estimate(
    p_order_id, p_business_id, p_estimate_type, p_estimated_minutes,
    p_estimated_ready_time, p_estimated_delivery_time, NULL,
    p_reason, p_notes, p_created_by_user_id, p_created_by_role
  ) INTO v_estimate_id;

  -- Send notification if requested
  IF p_send_notification THEN
    -- Get order details for notification
    SELECT o.user_id, o.order_number, o.customer_name
    INTO v_order_data
    FROM orders o
    WHERE o.id = p_order_id;

    -- Create notification content based on estimate type
    CASE p_estimate_type
      WHEN 'updated_prep' THEN
        v_notification_title := 'Order Preparation Update';
        v_notification_body := 'Your order #' || COALESCE(v_order_data.order_number, p_order_id::text) ||
                              ' preparation time has been updated to ' || p_estimated_minutes || ' minutes.';
      WHEN 'updated_delivery' THEN
        v_notification_title := 'Delivery Time Update';
        v_notification_body := 'Your order #' || COALESCE(v_order_data.order_number, p_order_id::text) ||
                              ' delivery time has been updated.';
      WHEN 'driver_eta' THEN
        v_notification_title := 'Driver Update';
        v_notification_body := 'Your driver has updated the estimated arrival time for order #' ||
                              COALESCE(v_order_data.order_number, p_order_id::text) || '.';
      ELSE
        v_notification_title := 'Order Update';
        v_notification_body := 'There has been an update to your order #' ||
                              COALESCE(v_order_data.order_number, p_order_id::text) || '.';
    END CASE;

    -- Insert notification log entry
    INSERT INTO notification_log (
      user_id, title, body, type, order_id, status
    ) VALUES (
      v_order_data.user_id, v_notification_title, v_notification_body,
      'order_update', p_order_id, 'pending'
    );
  END IF;

  RETURN v_estimate_id;
END;
$$ LANGUAGE plpgsql;

-- Function to record delivery update and trigger notifications
CREATE OR REPLACE FUNCTION update_order_delivery_progress(
  p_order_id INTEGER,
  p_driver_id INTEGER DEFAULT NULL,
  p_update_type VARCHAR(30),
  p_driver_location_lat DECIMAL(10, 8) DEFAULT NULL,
  p_driver_location_lng DECIMAL(11, 8) DEFAULT NULL,
  p_estimated_arrival_time TIMESTAMP WITH TIME ZONE DEFAULT NULL,
  p_delay_minutes INTEGER DEFAULT NULL,
  p_issue_type VARCHAR(50) DEFAULT NULL,
  p_notes TEXT DEFAULT NULL,
  p_send_notification BOOLEAN DEFAULT TRUE
) RETURNS INTEGER AS $$
DECLARE
  v_update_id INTEGER;
  v_order_data RECORD;
  v_notification_title TEXT;
  v_notification_body TEXT;
BEGIN
  -- Record the delivery update
  SELECT record_delivery_update(
    p_order_id, p_driver_id, p_update_type, p_driver_location_lat, p_driver_location_lng,
    NULL, p_estimated_arrival_time, p_delay_minutes, p_issue_type, NULL, p_notes, NULL
  ) INTO v_update_id;

  -- Send notification if requested and update type warrants it
  IF p_send_notification AND p_update_type IN ('pickup_arrived', 'pickup_completed', 'delivery_arrived', 'delivery_completed', 'delay', 'issue') THEN
    -- Get order details for notification
    SELECT o.user_id, o.order_number, o.customer_name
    INTO v_order_data
    FROM orders o
    WHERE o.id = p_order_id;

    -- Create notification content based on update type
    CASE p_update_type
      WHEN 'pickup_arrived' THEN
        v_notification_title := 'Driver Arrived at Restaurant';
        v_notification_body := 'Your driver has arrived to collect your order #' ||
                              COALESCE(v_order_data.order_number, p_order_id::text) || '.';
      WHEN 'pickup_completed' THEN
        v_notification_title := 'Order Picked Up';
        v_notification_body := 'Your order #' || COALESCE(v_order_data.order_number, p_order_id::text) ||
                              ' has been picked up and is on the way to you!';
      WHEN 'delivery_arrived' THEN
        v_notification_title := 'Driver Arrived';
        v_notification_body := 'Your driver has arrived with order #' ||
                              COALESCE(v_order_data.order_number, p_order_id::text) || '.';
      WHEN 'delivery_completed' THEN
        v_notification_title := 'Order Delivered';
        v_notification_body := 'Your order #' || COALESCE(v_order_data.order_number, p_order_id::text) ||
                              ' has been delivered successfully!';
      WHEN 'delay' THEN
        v_notification_title := 'Delivery Delay';
        v_notification_body := 'There is a ' || COALESCE(p_delay_minutes::text || ' minute', '') ||
                              ' delay with your order #' || COALESCE(v_order_data.order_number, p_order_id::text) || '.';
      WHEN 'issue' THEN
        v_notification_title := 'Delivery Update';
        v_notification_body := 'There is an update regarding your order #' ||
                              COALESCE(v_order_data.order_number, p_order_id::text) || '. Please check your messages.';
      ELSE
        v_notification_title := 'Delivery Update';
        v_notification_body := 'There has been an update to your order #' ||
                              COALESCE(v_order_data.order_number, p_order_id::text) || '.';
    END CASE;

    -- Insert notification log entry
    INSERT INTO notification_log (
      user_id, title, body, type, order_id, status
    ) VALUES (
      v_order_data.user_id, v_notification_title, v_notification_body,
      'delivery_update', p_order_id, 'pending'
    );
  END IF;

  RETURN v_update_id;
END;
$$ LANGUAGE plpgsql;

-- 11. Utility functions for analytics preparation

-- Function to get order time estimate history
CREATE OR REPLACE FUNCTION get_order_estimate_history(p_order_id INTEGER)
RETURNS TABLE (
  estimate_type VARCHAR(30),
  estimated_minutes INTEGER,
  estimated_ready_time TIMESTAMP WITH TIME ZONE,
  estimated_delivery_time TIMESTAMP WITH TIME ZONE,
  reason VARCHAR(100),
  created_by_role VARCHAR(20),
  created_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    ote.estimate_type,
    ote.estimated_minutes,
    ote.estimated_ready_time,
    ote.estimated_delivery_time,
    ote.reason,
    ote.created_by_role,
    ote.created_at
  FROM order_time_estimates ote
  WHERE ote.order_id = p_order_id
  ORDER BY ote.created_at;
END;
$$ LANGUAGE plpgsql;

-- Function to get order delivery progress
CREATE OR REPLACE FUNCTION get_order_delivery_progress(p_order_id INTEGER)
RETURNS TABLE (
  update_type VARCHAR(30),
  driver_location_lat DECIMAL(10, 8),
  driver_location_lng DECIMAL(11, 8),
  estimated_arrival_time TIMESTAMP WITH TIME ZONE,
  delay_minutes INTEGER,
  issue_type VARCHAR(50),
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    odu.update_type,
    odu.driver_location_lat,
    odu.driver_location_lng,
    odu.estimated_arrival_time,
    odu.delay_minutes,
    odu.issue_type,
    odu.notes,
    odu.created_at
  FROM order_delivery_updates odu
  WHERE odu.order_id = p_order_id
  ORDER BY odu.created_at;
END;
$$ LANGUAGE plpgsql;

-- 12. Grant necessary permissions
GRANT ALL ON order_time_estimates TO authenticated;
GRANT ALL ON order_delivery_updates TO authenticated;
GRANT EXECUTE ON FUNCTION record_time_estimate TO authenticated;
GRANT EXECUTE ON FUNCTION record_delivery_update TO authenticated;
GRANT EXECUTE ON FUNCTION update_order_time_estimate TO authenticated;
GRANT EXECUTE ON FUNCTION update_order_delivery_progress TO authenticated;
GRANT EXECUTE ON FUNCTION get_order_estimate_history TO authenticated;
GRANT EXECUTE ON FUNCTION get_order_delivery_progress TO authenticated;

-- Migration complete
SELECT 'Audit trail tables and functions created successfully' as status;
