import { NextResponse } from "next/server";
import { createClient } from "@supabase/supabase-js";
import { verifyBusinessAdminAccess } from "@/lib/simple-auth";

// Create a Supabase client with the service role key for admin access
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const adminClient = createClient(supabaseUrl, supabaseServiceKey);

export async function POST(request: Request) {
  try {
    console.log("Delivery settings API called");

    // Use the same auth pattern as other business-admin routes
    const accessCheck = await verifyBusinessAdminAccess(request)
    if (!accessCheck.authorized) {
      return NextResponse.json(
        { error: accessCheck.error },
        { status: accessCheck.status || 401 }
      )
    }

    const userProfile = accessCheck.profile;
    const isAdmin = userProfile.role === "admin" || userProfile.role === "super_admin";

    // Get the request body
    const deliveryConfig = await request.json();
    console.log("Received delivery config:", deliveryConfig);

    // Determine which business to update
    let targetBusinessId: number | null = null;

    if (!isAdmin) {
      // Regular user - get their associated business from business_managers table
      const { data: managerData, error: managerError } = await adminClient
        .from("business_managers")
        .select("business_id")
        .eq("user_id", userProfile.id)
        .maybeSingle();

      if (managerError) {
        console.error("Error fetching business manager data:", managerError);
        return NextResponse.json(
          { error: "Error fetching business association" },
          { status: 500 }
        );
      }

      if (!managerData || !managerData.business_id) {
        return NextResponse.json(
          { error: "No business associated with this account" },
          { status: 403 }
        );
      }

      targetBusinessId = managerData.business_id;
    } else {
      // For admin users, we need a business ID parameter
      const url = new URL(request.url);
      const businessId = url.searchParams.get('businessId');
      
      if (!businessId) {
        return NextResponse.json(
          { error: "Admin users must specify a business ID parameter" },
          { status: 400 }
        );
      }
      
      targetBusinessId = parseInt(businessId);
    }

    console.log("Updating delivery settings for business ID:", targetBusinessId);

    // Update the business_delivery_config table
    const { data: updatedConfig, error: updateError } = await adminClient
      .from("business_delivery_config")
      .update({
        pickup_enabled: deliveryConfig.pickup_enabled,
        pickup_available: deliveryConfig.pickup_available,
        delivery_enabled: deliveryConfig.delivery_enabled,
        delivery_available: deliveryConfig.delivery_available,
        use_loop_delivery: deliveryConfig.use_loop_delivery,
        restriction_logic: deliveryConfig.restriction_logic,
        delivery_radius: deliveryConfig.delivery_radius,
        pickup_asap_available: deliveryConfig.pickup_asap_available,
        pickup_scheduled_available: deliveryConfig.pickup_scheduled_available,
        delivery_asap_available: deliveryConfig.delivery_asap_available,
        delivery_scheduled_available: deliveryConfig.delivery_scheduled_available,
        min_advance_booking_minutes: deliveryConfig.min_advance_booking_minutes,
        max_advance_booking_days: deliveryConfig.max_advance_booking_days,
        minimum_order_value: deliveryConfig.minimum_order_value,
        maximum_order_value: deliveryConfig.maximum_order_value,
        updated_at: new Date().toISOString()
      })
      .eq("business_id", targetBusinessId)
      .select()
      .single();

    if (updateError) {
      console.error("Error updating delivery config:", updateError);
      return NextResponse.json(
        { error: "Failed to update delivery settings" },
        { status: 500 }
      );
    }

    console.log("Successfully updated delivery settings");

    return NextResponse.json({
      success: true,
      message: "Delivery settings updated successfully",
      config: updatedConfig
    });

  } catch (error) {
    console.error("Delivery settings API error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
