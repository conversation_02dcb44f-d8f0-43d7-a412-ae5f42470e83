import type { Restaurant, Shop } from '@/types/business';

// Get Supabase URL and keys from environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';

// Helper function to make API calls to Supabase
async function fetchFromSupabase(path: string, params: Record<string, string> = {}) {
  // Build query string
  const queryString = Object.keys(params).length > 0
    ? '?' + new URLSearchParams(params).toString()
    : '';

  // Make API call
  const response = await fetch(`${supabaseUrl}/rest/v1/${path}${queryString}`, {
    headers: {
      'apikey': supabaseAnonKey,
      'Authorization': `Bearer ${supabaseAnonKey}`,
      'Content-Type': 'application/json'
    }
  });

  if (!response.ok) {
    throw new Error(`API call failed: ${response.status} ${response.statusText}`);
  }

  return response.json();
}

// Get all businesses of a specific type
export async function getAllBusinessesByType(
  type: 'restaurant' | 'shop' | 'pharmacy' | 'cafe' | 'errand',
  postcode?: string,
  userId?: string
): Promise<any[]> {
  try {
    // Get business type ID
    const businessTypes = await fetchFromSupabase('business_types', {
      'select': '*',
      'slug': 'eq.' + type
    });

    if (!businessTypes || businessTypes.length === 0) {
      return [];
    }

    const businessTypeId = businessTypes[0].id;

    // Fetch approved businesses only with custom categories
    const businesses = await fetchFromSupabase('businesses', {
      'select': '*',
      'business_type_id': 'eq.' + businessTypeId,
      'is_approved': 'eq.true'
    });



    // Fetch custom categories for all businesses in one query for efficiency
    const businessIds = businesses.map(b => b.id);
    let customCategoriesMap = new Map();

    if (businessIds.length > 0) {
      const customCategories = await fetchFromSupabase('business_custom_categories', {
        'select': 'business_id,name',
        'business_id': 'in.(' + businessIds.join(',') + ')',
        'is_active': 'eq.true'
      });

      // Group custom categories by business_id
      customCategories.forEach(category => {
        if (!customCategoriesMap.has(category.business_id)) {
          customCategoriesMap.set(category.business_id, []);
        }
        customCategoriesMap.get(category.business_id).push(category.name);
      });
    }

    // Use provided postcode or get from localStorage if available
    let userPostcode: string | null = postcode || null;

    // If no postcode was provided, try to get it from localStorage
    if (!userPostcode && typeof window !== 'undefined') {
      // The correct localStorage key is 'loop_jersey_postcode'
      userPostcode = localStorage.getItem('loop_jersey_postcode');
      console.log('Retrieved postcode from localStorage:', userPostcode);
    }

    // Transform businesses to match the expected format
    const transformedBusinesses = await Promise.all(businesses.map(async (business: any) => {
      // Default attributes based on business type
      let attributes: string[] = [];
      switch (type) {
        case 'restaurant':
          attributes = ['Italian', 'Casual Dining'];
          break;
        case 'shop':
          attributes = ['Grocery', 'Convenience'];
          break;
        case 'pharmacy':
          attributes = ['Prescription', 'Over-the-counter'];
          break;
        case 'cafe':
          attributes = ['Coffee', 'Bakery'];
          break;
        case 'errand':
          attributes = ['General'];
          break;
        default:
          attributes = ['General'];
      }

      // Get custom categories for this business
      const customCategories = customCategoriesMap.get(business.id) || [];

      // Ensure we have a valid postcode - use JE1 as default if none provided
      const postcode = userPostcode || 'JE1';

      // Ensure we have valid delivery fee model and rates
      const deliveryFeeModel = business.delivery_fee_model || 'mixed';
      const deliveryFee = typeof business.delivery_fee === 'number' ? business.delivery_fee : 2.0;
      const deliveryFeePerKm = typeof business.delivery_fee_per_km === 'number' ? business.delivery_fee_per_km : 1.0;

      // Parse business coordinates from string format to array
      let parsedCoordinates: [number, number] | null = null;
      if (business.coordinates) {
        try {
          if (typeof business.coordinates === 'string') {
            // Parse string format like "(-2.109951,49.185141)"
            const coordStr = business.coordinates.replace(/[()]/g, '');
            const [lng, lat] = coordStr.split(',').map(Number);
            if (!isNaN(lng) && !isNaN(lat)) {
              parsedCoordinates = [lng, lat];
            }
          } else if (Array.isArray(business.coordinates)) {
            parsedCoordinates = business.coordinates;
          }
        } catch (error) {
          console.error('Error parsing business coordinates:', business.coordinates, error);
        }
      }

      // Simple delivery estimates for now
      const deliveryEstimates = {
        time: business.preparation_time_minutes || 25,
        timeRange: `${Math.max(15, (business.preparation_time_minutes || 25) - 5)}-${(business.preparation_time_minutes || 25) + 5} min`,
        fee: `£${(business.delivery_fee || 2.50).toFixed(2)}`,
        feeNumeric: business.delivery_fee || 2.50,
        distance: '2.5 km'
      };

      // Calculate actual rating from Reviews table
      let actualRating = 0;
      let actualReviewCount = 0;

      try {
        // Fetch reviews for this business to calculate actual rating
        const reviews = await fetchFromSupabase('reviews', {
          'select': 'rating',
          'business_id': 'eq.' + business.id
        });

        if (reviews && reviews.length > 0) {
          const totalRating = reviews.reduce((sum: number, review: any) => sum + review.rating, 0);
          actualRating = Math.round((totalRating / reviews.length) * 10) / 10; // Round to 1 decimal place
          actualReviewCount = reviews.length;
        }
      } catch (error) {
        console.error(`Error fetching reviews for business ${business.id}:`, error);
        // Fall back to database values if review fetch fails
        actualRating = business.rating || 0;
        actualReviewCount = business.review_count || 0;
      }

      // Transform to match the appropriate type
      const baseBusinessData = {
        id: business.slug,
        name: business.name,
        businessType: type,
        coverImage: business.banner_url || '/placeholder.svg',
        image_url: business.logo_url || business.banner_url || '/placeholder.svg',
        rating: actualRating, // Use calculated rating from Reviews table
        reviewCount: actualReviewCount, // Use actual review count from Reviews table
        deliveryTime: deliveryEstimates.time,
        deliveryTimeRange: deliveryEstimates.timeRange,
        preparationTimeMinutes: business.preparation_time_minutes,
        preparation_time_minutes: business.preparation_time_minutes,
        // Store both the numeric value and formatted string for the delivery fee
        // Handle parsing safely to avoid NaN
        deliveryFee: deliveryEstimates.feeNumeric || business.delivery_fee || 0,
        deliveryFeeFormatted: deliveryEstimates.fee,
        distance: deliveryEstimates.distance,
        isNew: false,
        offer: null,
        location: business.location,
        phone: '************', // Default phone
        description: business.description,
        coordinates: parsedCoordinates,
        delivery_fee_model: business.delivery_fee_model,
        delivery_fee: business.delivery_fee,
        delivery_fee_per_km: business.delivery_fee_per_km,
        delivery_radius: business.delivery_radius,
        delivery_available: business.delivery_available, // Use business delivery_available flag
        customCategories: customCategories, // Add custom categories for search
        // Business status and closure fields
        is_temporarily_closed: business.is_temporarily_closed,
        closure_message: business.closure_message,
        pickup_available: business.pickup_available,
        opening_hours: business.opening_hours
      };

      // Add type-specific properties
      switch (type) {
        case 'restaurant':
          return {
            ...baseBusinessData,
            cuisines: attributes,
            menuCategories: [] // Will be populated when needed
          } as Restaurant;
        case 'shop':
          return {
            ...baseBusinessData,
            storeTypes: attributes,
            productCategories: [] // Will be populated when needed
          } as Shop;
        case 'pharmacy':
          return {
            ...baseBusinessData,
            pharmacyTypes: attributes,
            productCategories: [] // Will be populated when needed
          };
        case 'cafe':
          return {
            ...baseBusinessData,
            cafeTypes: attributes,
            menuCategories: [] // Will be populated when needed
          };
        case 'errand':
          return {
            ...baseBusinessData,
            errandTypes: attributes,
            errandCategories: [] // Will be populated when needed
          };
        default:
          return {
            ...baseBusinessData,
            genericTypes: attributes
          };
      }
    }));

    return transformedBusinesses;
  } catch (error) {
    console.error('Error in getAllBusinessesByType:', error);
    return [];
  }
}

// Get all restaurants
export async function getAllRestaurants(postcode?: string, userId?: string): Promise<Restaurant[]> {
  return getAllBusinessesByType('restaurant', postcode, userId) as Promise<Restaurant[]>;
}

// Get all shops
export async function getAllShops(postcode?: string, userId?: string): Promise<Shop[]> {
  return getAllBusinessesByType('shop', postcode, userId) as Promise<Shop[]>;
}

// Get all pharmacy services
export async function getAllPharmacyServices(postcode?: string, userId?: string): Promise<any[]> {
  return getAllBusinessesByType('pharmacy' as any, postcode, userId);
}

// Get all cafe services
export async function getAllCafeServices(postcode?: string, userId?: string): Promise<any[]> {
  return getAllBusinessesByType('cafe' as any, postcode, userId);
}

// Get all errand services
export async function getAllErrandServices(postcode?: string, userId?: string): Promise<any[]> {
  return getAllBusinessesByType('errand', postcode, userId);
}

// This function is now replaced by calculateBusinessDeliveryEstimates in business-delivery-utils.ts
