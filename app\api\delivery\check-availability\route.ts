import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { getParishFromPostcodeSync } from '@/lib/jersey-postcodes'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const businessId = searchParams.get('businessId')
    const postcode = searchParams.get('postcode')

    if (!businessId || !postcode) {
      return NextResponse.json(
        { error: 'Business ID and postcode are required' },
        { status: 400 }
      )
    }

    // Get the parish from the postcode
    const parish = getParishFromPostcodeSync(postcode)
    if (!parish) {
      return NextResponse.json(
        { error: 'Invalid postcode' },
        { status: 400 }
      )
    }

    // Get the parish ID
    const { data: parishData, error: parishError } = await supabase
      .from('parishes')
      .select('id')
      .eq('name', parish)
      .single()

    if (parishError || !parishData) {
      console.error('Error finding parish:', parishError)
      return NextResponse.json(
        { available: true }, // Graceful degradation
        { status: 200 }
      )
    }

    // Check if the business has delivery restrictions for this parish
    const { data: deliveryConfig, error: configError } = await supabase
      .from('business_parish_delivery')
      .select('*')
      .eq('business_id', parseInt(businessId))
      .eq('parish_id', parishData.id)
      .single()

    if (configError && configError.code !== 'PGRST116') { // PGRST116 = no rows returned
      console.error('Error checking delivery config:', configError)
      return NextResponse.json(
        { available: true }, // Graceful degradation
        { status: 200 }
      )
    }

    // If there's a record in business_parish_delivery, delivery is allowed
    // If there's no record, check if the business has any parish restrictions at all
    if (deliveryConfig) {
      return NextResponse.json({
        available: true,
        parish,
        deliveryFee: deliveryConfig.delivery_fee_override,
        minimumOrder: deliveryConfig.minimum_order_override,
        estimatedMinutes: deliveryConfig.estimated_delivery_minutes
      })
    }

    // Check if the business has any parish delivery restrictions
    const { data: hasRestrictions, error: restrictionsError } = await supabase
      .from('business_parish_delivery')
      .select('business_id')
      .eq('business_id', parseInt(businessId))
      .limit(1)

    if (restrictionsError) {
      console.error('Error checking for restrictions:', restrictionsError)
      return NextResponse.json(
        { available: true }, // Graceful degradation
        { status: 200 }
      )
    }

    // If the business has parish restrictions but this parish is not in the allowed list,
    // delivery is not available
    if (hasRestrictions && hasRestrictions.length > 0) {
      return NextResponse.json({
        available: false,
        parish,
        reason: 'Parish not in delivery area'
      })
    }

    // If the business has no parish restrictions, delivery is available
    return NextResponse.json({
      available: true,
      parish
    })

  } catch (error) {
    console.error('Error in delivery availability check:', error)
    return NextResponse.json(
      { available: true }, // Graceful degradation
      { status: 200 }
    )
  }
}
