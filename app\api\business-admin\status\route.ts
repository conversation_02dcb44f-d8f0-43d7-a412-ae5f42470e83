import { NextRequest, NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

// Helper function to get closure status from opening hours
function getBusinessClosureStatus(openingHours: any[]): boolean {
  if (!openingHours || openingHours.length === 0) return false;
  const today = new Date().getDay(); // 0 = Sunday, 1 = Monday, etc.
  const todayHours = openingHours.find(hours => hours.day_of_week === today);
  return todayHours?.is_closed === true;
}

// Helper function to get closure message from opening hours
function getBusinessClosureMessage(openingHours: any[]): string | null {
  if (!openingHours || openingHours.length === 0) return null;
  const today = new Date().getDay(); // 0 = Sunday, 1 = Monday, etc.
  const todayHours = openingHours.find(hours => hours.day_of_week === today);
  return todayHours?.closure_message || null;
}

/**
 * Update business temporary closure status
 * PATCH /api/business-admin/status
 */
export async function PATCH(request: NextRequest) {
  try {
    const body = await request.json()
    const { businessId, isTemporarilyClosed, closureMessage } = body

    console.log('🏪 Business status update request:', {
      businessId,
      isTemporarilyClosed,
      closureMessage: closureMessage ? `"${closureMessage}"` : 'null'
    })

    // Validate input
    if (!businessId || typeof businessId !== 'number') {
      return NextResponse.json(
        { error: 'Valid business ID is required' },
        { status: 400 }
      )
    }

    if (typeof isTemporarilyClosed !== 'boolean') {
      return NextResponse.json(
        { error: 'isTemporarilyClosed must be a boolean' },
        { status: 400 }
      )
    }

    // Validate closure message length if provided
    if (closureMessage && typeof closureMessage === 'string' && closureMessage.length > 200) {
      return NextResponse.json(
        { error: 'Closure message cannot exceed 200 characters' },
        { status: 400 }
      )
    }

    // Create Supabase client with service role for admin operations
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Get current date info
    const today = new Date().getDay(); // 0 = Sunday, 1 = Monday, etc.

    // Prepare update data for opening hours table
    const updateData: any = {
      is_closed: isTemporarilyClosed,
      closure_message: isTemporarilyClosed ? (closureMessage || null) : null,
      updated_at: new Date().toISOString()
    }

    console.log('📝 Prepared update data for opening hours:', updateData)

    // Upsert the business opening hours for today (create if doesn't exist, update if exists)
    const { data, error } = await supabase
      .from('business_opening_hours')
      .upsert({
        business_id: businessId,
        day_of_week: today,
        ...updateData
      }, {
        onConflict: 'business_id,day_of_week'
      })
      .select('business_id, day_of_week, is_closed, closure_message')
      .single()

    if (error) {
      console.error('❌ Error updating business status:', error)
      return NextResponse.json(
        { error: 'Failed to update business status' },
        { status: 500 }
      )
    }

    if (!data) {
      return NextResponse.json(
        { error: 'Business not found' },
        { status: 404 }
      )
    }

    console.log('✅ Business opening hours updated successfully:', {
      businessId: data.business_id,
      dayOfWeek: data.day_of_week,
      isClosed: data.is_closed,
      closureMessage: data.closure_message
    })

    return NextResponse.json({
      success: true,
      data: {
        businessId: data.business_id,
        dayOfWeek: data.day_of_week,
        isTemporarilyClosed: data.is_closed,
        closureMessage: data.closure_message
      }
    })

  } catch (error: any) {
    console.error('❌ Business status update error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

/**
 * Get business status
 * GET /api/business-admin/status?businessId=123
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const businessId = searchParams.get('businessId')

    if (!businessId) {
      return NextResponse.json(
        { error: 'Business ID is required' },
        { status: 400 }
      )
    }

    const businessIdNum = parseInt(businessId, 10)
    if (isNaN(businessIdNum)) {
      return NextResponse.json(
        { error: 'Invalid business ID' },
        { status: 400 }
      )
    }

    // Create Supabase client with service role
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Get business status with opening hours for closure information
    const { data, error } = await supabase
      .from('businesses')
      .select(`
        id,
        name,
        business_opening_hours (
          day_of_week,
          is_closed,
          closure_message
        )
      `)
      .eq('id', businessIdNum)
      .single()

    if (error) {
      console.error('❌ Error fetching business status:', error)
      return NextResponse.json(
        { error: 'Failed to fetch business status' },
        { status: 500 }
      )
    }

    if (!data) {
      return NextResponse.json(
        { error: 'Business not found' },
        { status: 404 }
      )
    }

    // Extract closure information from opening hours
    const isTemporarilyClosed = getBusinessClosureStatus(data.business_opening_hours)
    const closureMessage = getBusinessClosureMessage(data.business_opening_hours)

    return NextResponse.json({
      success: true,
      data: {
        id: data.id,
        name: data.name,
        isTemporarilyClosed: isTemporarilyClosed,
        closureMessage: closureMessage || ""
      }
    })

  } catch (error: any) {
    console.error('❌ Business status fetch error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
