/* Business Admin UI Improvements */

/* White background */
.business-admin-container {
  background-color: white !important;
}

/* Mobile-friendly improvements */
@media (max-width: 640px) {
  .business-admin-container {
    font-size: 14px;
  }

  /* Ensure touch targets are at least 44px */
  .business-admin-container button {
    min-height: 44px;
  }

  /* Better spacing for mobile */
  .business-admin-container .container-fluid {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }
}

/* Enhanced card styling */
.business-admin-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;
  transition: all 0.2s ease-in-out;
}

.business-admin-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

/* Stats cards */
.stats-card {
  background-color: white;
  border-left-width: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.stats-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

/* Table styling */
.orders-table {
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
}

.orders-table th {
  background-color: #f9fafb;
  font-weight: 600;
  color: #374151;
}

.orders-table tr:hover {
  background-color: #f9fafb;
}

/* Status badges */
.status-badge {
  font-weight: 500;
  padding: 4px 8px;
  border-radius: 9999px;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
}

.status-badge-pending {
  background-color: #fffbeb;
  color: #b45309;
  border: 1px solid #fcd34d;
}

.status-badge-pending:hover {
  background-color: #fef3c7;
  color: #92400e;
}

.status-badge-confirmed {
  background-color: #fefce8;
  color: #a16207;
  border: 1px solid #facc15;
}

.status-badge-confirmed:hover {
  background-color: #fef08a;
  color: #854d0e;
}

.status-badge-processing,
.status-badge-preparing {
  background-color: #eff6ff;
  color: #1d4ed8;
  border: 1px solid #93c5fd;
}

.status-badge-processing:hover,
.status-badge-preparing:hover {
  background-color: #dbeafe;
  color: #1e40af;
}

.status-badge-ready {
  background-color: #eef2ff;
  color: #4338ca;
  border: 1px solid #a5b4fc;
}

.status-badge-ready:hover {
  background-color: #e0e7ff;
  color: #3730a3;
}

.status-badge-out-for-delivery {
  background-color: #faf5ff;
  color: #7c3aed;
  border: 1px solid #c4b5fd;
}

.status-badge-out-for-delivery:hover {
  background-color: #f3e8ff;
  color: #6d28d9;
}

.status-badge-completed,
.status-badge-delivered {
  background-color: #ecfdf5;
  color: #15803d;
  border: 1px solid #86efac;
}

.status-badge-completed:hover,
.status-badge-delivered:hover {
  background-color: #d1fae5;
  color: #166534;
}

.status-badge-cancelled {
  background-color: #fef2f2;
  color: #dc2626;
  border: 1px solid #fca5a5;
}

.status-badge-cancelled:hover {
  background-color: #fee2e2;
  color: #b91c1c;
}

/* Tabs styling */
.admin-tabs {
  background-color: white;
  padding: 4px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.admin-tab {
  font-weight: 500;
  padding: 8px 16px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.admin-tab[data-state="active"] {
  background-color: #f9fafb;
  color: #111827;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* Buttons */
.admin-button {
  font-weight: 500;
  transition: all 0.2s ease;
}

.admin-button-primary {
  background-color: #10b981;
  color: white;
}

.admin-button-primary:hover {
  background-color: #059669;
}

/* Filter section */
.filter-section {
  background-color: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}
