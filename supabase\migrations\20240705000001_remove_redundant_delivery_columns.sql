-- Remove redundant delivery/pickup columns from businesses table
-- These are now handled by the business_delivery_config table

-- First, ensure all businesses have a corresponding delivery config record
INSERT INTO business_delivery_config (business_id)
SELECT id
FROM businesses
WHERE id NOT IN (SELECT business_id FROM business_delivery_config)
ON CONFLICT (business_id) DO NOTHING;

-- Update orders_view to use business_delivery_config instead of businesses table
DROP VIEW IF EXISTS orders_view;

CREATE VIEW orders_view AS
SELECT
  o.id,
  o.created_at,
  o.updated_at,
  o.customer_name,
  o.customer_email,
  o.customer_phone,
  o.delivery_address,
  o.customer_coordinates,
  o.delivery_instructions,
  o.payment_method,
  o.payment_status,
  o.total,
  o.order_number,
  o.business_id,
  o.business_name,
  o.business_type,
  o.subtotal,
  o.delivery_fee,
  o.service_fee,
  o.status,
  o.preparation_time,
  o.estimated_delivery_time,
  o.business_slug,
  o.delivery_method,
  o.scheduled_time,
  o.delivery_type,
  o.cart_id,
  o.session_id,
  o.parish,
  o.postcode,
  o.user_id,
  o.ready_time,
  b.name AS business_name_from_table,
  b.address AS business_address,
  b.phone AS business_phone,
  CASE
    WHEN COALESCE(bdc.use_loop_delivery, true) = true THEN 'loop'
    WHEN COALESCE(bdc.use_loop_delivery, true) = false THEN 'internal'
    ELSE 'loop'
  END AS delivery_fulfillment
FROM orders o
LEFT JOIN businesses b ON o.business_id = b.id
LEFT JOIN business_delivery_config bdc ON b.id = bdc.business_id;

-- Remove redundant columns that are now in business_delivery_config
ALTER TABLE businesses
DROP COLUMN IF EXISTS delivery_available,
DROP COLUMN IF EXISTS pickup_available,
DROP COLUMN IF EXISTS use_loop_delivery,
DROP COLUMN IF EXISTS pickup_asap_available,
DROP COLUMN IF EXISTS pickup_scheduled_time_available,
DROP COLUMN IF EXISTS pickup_scheduled_period_available,
DROP COLUMN IF EXISTS delivery_asap_available,
DROP COLUMN IF EXISTS delivery_scheduled_time_available,
DROP COLUMN IF EXISTS delivery_scheduled_period_available,
DROP COLUMN IF EXISTS pickup_enabled,
DROP COLUMN IF EXISTS pickup_scheduled_available;

-- Keep delivery_fee, delivery_fee_model, delivery_fee_per_km, delivery_radius for now
-- as they may still be used by some parts of the system until fully migrated

-- Add comment
COMMENT ON TABLE businesses IS 'Core business information. Delivery configuration moved to business_delivery_config table.';
