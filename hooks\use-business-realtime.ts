'use client'

import { useState, useEffect } from 'react'
import { supabaseRealtime, getAuthenticatedRealtimeClient } from '@/lib/supabase'

// Hook for subscribing to real-time business updates
export function useBusinessRealtime(businessId: string) {
  const [prepTime, setPrepTime] = useState<number | null>(null)
  const [deliveryFee, setDeliveryFee] = useState<number | null>(null)
  const [deliveryFeeModel, setDeliveryFeeModel] = useState<string | null>(null)
  const [deliveryFeePerKm, setDeliveryFeePerKm] = useState<number | null>(null)
  const [coordinates, setCoordinates] = useState<[number, number] | null>(null)
  const [deliveryAvailable, setDeliveryAvailable] = useState<boolean | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (!businessId) {
      setIsLoading(false)
      return
    }

    setIsLoading(true)
    setError(null)

    // No initial API fetch needed - real-time subscription will provide all data
    console.log('Setting up business real-time subscription without initial fetch...');

    // Set up Supabase subscription for real-time updates
    // Use the authenticated client to ensure we have the right permissions
    const client = getAuthenticatedRealtimeClient()
    const channel = client
      .channel(`business-${businessId}`)
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'businesses',
          filter: `slug=eq.${businessId}`
        },
        (payload) => {
          console.log('Received real-time update for business:', businessId, payload)

          // Update preparation time when it changes
          if (payload.new && payload.new.preparation_time_minutes !== undefined) {
            console.log(`Updating preparation time from ${prepTime} to ${payload.new.preparation_time_minutes}`)
            setPrepTime(payload.new.preparation_time_minutes)
          }

          // Update delivery fee when it changes
          if (payload.new && payload.new.delivery_fee !== undefined) {
            console.log(`Updating delivery fee from ${deliveryFee} to ${payload.new.delivery_fee}`)
            setDeliveryFee(payload.new.delivery_fee)
          }

          // Update delivery availability when it changes
          if (payload.new && payload.new.delivery_available !== undefined) {
            console.log(`Updating delivery availability from ${deliveryAvailable} to ${payload.new.delivery_available}`)
            setDeliveryAvailable(payload.new.delivery_available)
          }

          // Update delivery fee model when it changes
          if (payload.new && payload.new.delivery_fee_model !== undefined) {
            console.log(`Updating delivery fee model from ${deliveryFeeModel} to ${payload.new.delivery_fee_model}`)
            setDeliveryFeeModel(payload.new.delivery_fee_model)
          }

          // Update delivery fee per km when it changes
          if (payload.new && payload.new.delivery_fee_per_km !== undefined) {
            console.log(`Updating delivery fee per km from ${deliveryFeePerKm} to ${payload.new.delivery_fee_per_km}`)
            setDeliveryFeePerKm(payload.new.delivery_fee_per_km)
          }

          // Update coordinates when they change
          if (payload.new && payload.new.coordinates) {
            try {
              let parsedCoordinates: [number, number] | null = null;
              const newCoords = payload.new.coordinates;

              if (typeof newCoords === 'string') {
                // Try to parse string coordinates
                if (newCoords.startsWith('[') && newCoords.endsWith(']')) {
                  // JSON array format
                  const coords = JSON.parse(newCoords);
                  if (Array.isArray(coords) && coords.length === 2) {
                    parsedCoordinates = [parseFloat(coords[0]), parseFloat(coords[1])];
                  }
                } else if (newCoords.startsWith('(') && newCoords.endsWith(')')) {
                  // Parentheses format like "(lng,lat)"
                  const coordsStr = newCoords.substring(1, newCoords.length - 1);
                  const parts = coordsStr.split(',').map(part => parseFloat(part.trim()));
                  if (parts.length === 2 && !isNaN(parts[0]) && !isNaN(parts[1])) {
                    parsedCoordinates = [parts[0], parts[1]];
                  }
                } else {
                  // Simple comma-separated format
                  const parts = newCoords.split(',').map(part => parseFloat(part.trim()));
                  if (parts.length === 2 && !isNaN(parts[0]) && !isNaN(parts[1])) {
                    parsedCoordinates = [parts[0], parts[1]];
                  }
                }
              } else if (Array.isArray(newCoords) && newCoords.length === 2) {
                // Array format
                parsedCoordinates = [
                  parseFloat(newCoords[0]),
                  parseFloat(newCoords[1])
                ];
              }

              if (parsedCoordinates) {
                console.log(`Updating coordinates for ${businessId} from`, coordinates, 'to', parsedCoordinates);
                setCoordinates(parsedCoordinates);
              }
            } catch (parseError) {
              console.error('Error parsing updated business coordinates:', parseError);
            }
          }
        }
      )
      .subscribe(async (status) => {
        console.log(`Realtime subscription status for business ${businessId}:`, status)
        if (status === 'SUBSCRIBED') {
          console.log(`Successfully subscribed to real-time updates for business ${businessId}`)

          // Fetch initial data when subscription is established
          try {
            const { data, error } = await client
              .from('businesses')
              .select(`
                preparation_time_minutes,
                coordinates,
                business_delivery_config!inner(
                  delivery_enabled,
                  delivery_available
                ),
                business_delivery_pricing(
                  base_delivery_fee,
                  pricing_model,
                  fee_per_km
                )
              `)
              .eq('slug', businessId)
              .single()

            if (error) {
              console.error('Error fetching initial business data:', error)
              setError('Failed to fetch business data')
            } else if (data) {
              console.log('Initial business data from real-time subscription:', data)
              setPrepTime(data.preparation_time_minutes)

              // Handle delivery config data
              const deliveryConfig = data.business_delivery_config
              const deliveryPricing = data.business_delivery_pricing?.[0]

              if (deliveryConfig) {
                setDeliveryAvailable(deliveryConfig.delivery_enabled && deliveryConfig.delivery_available)
              }

              if (deliveryPricing) {
                setDeliveryFee(deliveryPricing.base_delivery_fee)
                setDeliveryFeeModel(deliveryPricing.pricing_model)
                setDeliveryFeePerKm(deliveryPricing.fee_per_km)
              }

              // Parse coordinates if available
              if (data.coordinates) {
                try {
                  let parsedCoordinates: [number, number] | null = null;

                  if (typeof data.coordinates === 'string') {
                    // Try to parse string coordinates
                    if (data.coordinates.startsWith('[') && data.coordinates.endsWith(']')) {
                      // JSON array format
                      const coords = JSON.parse(data.coordinates);
                      if (Array.isArray(coords) && coords.length === 2) {
                        parsedCoordinates = [parseFloat(coords[0]), parseFloat(coords[1])];
                      }
                    } else if (data.coordinates.startsWith('(') && data.coordinates.endsWith(')')) {
                      // Parentheses format like "(lng,lat)"
                      const coordsStr = data.coordinates.substring(1, data.coordinates.length - 1);
                      const parts = coordsStr.split(',').map(part => parseFloat(part.trim()));
                      if (parts.length === 2 && !isNaN(parts[0]) && !isNaN(parts[1])) {
                        parsedCoordinates = [parts[0], parts[1]];
                      }
                    } else {
                      // Simple comma-separated format
                      const parts = data.coordinates.split(',').map(part => parseFloat(part.trim()));
                      if (parts.length === 2 && !isNaN(parts[0]) && !isNaN(parts[1])) {
                        parsedCoordinates = [parts[0], parts[1]];
                      }
                    }
                  } else if (Array.isArray(data.coordinates) && data.coordinates.length === 2) {
                    // Array format
                    parsedCoordinates = [
                      parseFloat(data.coordinates[0]),
                      parseFloat(data.coordinates[1])
                    ];
                  }

                  if (parsedCoordinates) {
                    console.log(`Parsed coordinates for ${businessId}:`, parsedCoordinates);
                    setCoordinates(parsedCoordinates);
                  }
                } catch (parseError) {
                  console.error('Error parsing business coordinates:', parseError);
                }
              }
            }
          } catch (err) {
            console.error('Exception fetching initial business data:', err)
            setError('An error occurred while fetching business data')
          } finally {
            setIsLoading(false)
          }
        } else if (status === 'CHANNEL_ERROR') {
          console.error(`Error subscribing to real-time updates for business ${businessId}`)
          // Don't set error state for channel errors - continue with cached data
          setIsLoading(false)
        } else if (status === 'CLOSED') {
          console.log(`Real-time channel closed for business ${businessId}`)
          setIsLoading(false)
        }
      })

    // Clean up subscription on unmount
    return () => {
      console.log(`Unsubscribing from real-time updates for business ${businessId}`)
      supabaseRealtime.removeChannel(channel)
    }
  }, [businessId]) // Removed state dependencies to prevent infinite loop

  return {
    prepTime,
    deliveryFee,
    deliveryFeeModel,
    deliveryFeePerKm,
    coordinates,
    deliveryAvailable,
    isLoading,
    error
  }
}
