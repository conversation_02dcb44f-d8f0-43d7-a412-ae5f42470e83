"use client";

import React from 'react';
import { useDeliveryCalculation } from '@/hooks/use-delivery-calculation';
import PortraitBusinessCard from './portrait-business-card';

interface UnifiedBusinessCardProps {
  business: {
    id: string;
    name: string;
    image_url?: string;
    logo_url?: string;
    coverImage?: string;
    businessType?: string;
    rating?: number;
    location?: string;
    delivery_radius?: number;
    delivery_available?: boolean;
    coordinates?: [number, number] | null;
    delivery_fee_model?: 'fixed' | 'distance' | 'mixed';
    delivery_fee?: number;
    delivery_fee_per_km?: number;
    preparation_time_minutes?: number;
    preparationTimeMinutes?: number;
    distance?: string;
    deal?: string;
    offer?: string;
    // Pre-calculated delivery data from business service
    deliveryFee?: number;
    deliveryFeeFormatted?: string;
    deliveryTime?: string | number;
    deliveryTimeRange?: string;
  };
  index: number;
  enableRealTimeUpdates?: boolean;
  realtimePrepTime?: number | null;

}

/**
 * Unified business card component that uses the same delivery calculation logic
 * for both search page and business detail page
 */
export default function UnifiedBusinessCard({
  business,
  index,
  enableRealTimeUpdates = false,
  realtimePrepTime = null
}: UnifiedBusinessCardProps) {

  // Only use the delivery calculation hook for real-time updates
  // For search page, avoid redundant API calls by using pre-calculated values
  const deliveryData = enableRealTimeUpdates ? useDeliveryCalculation(
    business.id,
    business.name,
    business.coordinates,
    business.preparationTimeMinutes || business.preparation_time_minutes || 15,
    business.delivery_fee_model || 'fixed',
    business.delivery_fee || 2.50,
    business.delivery_fee_per_km || 0.50,
    {
      enableRealTimeUpdates,
      realtimePrepTime
    }
  ) : {
    fee: business.deliveryFeeFormatted || `£${(business.deliveryFee || business.delivery_fee || 2.50).toFixed(2)}`,
    feeNumeric: business.deliveryFee || business.delivery_fee || 2.50,
    time: business.deliveryTime?.toString() || '30',
    timeRange: business.deliveryTimeRange || '25-35 min',
    travelTimeMinutes: 15,
    totalTimeMinutes: parseInt(business.deliveryTime?.toString() || '30'),
    isLoading: false
  };

  // Get image URL - prioritize image_url which should contain logo_url from the business service
  const imageUrl = business.image_url || business.logo_url || business.coverImage || '/placeholder.svg';

  // Determine business type
  const businessType = business.businessType || 'restaurant';

  // Format preparation time for display
  const preparationTime = realtimePrepTime !== null
    ? `${realtimePrepTime} min`
    : business.preparationTimeMinutes
      ? `${business.preparationTimeMinutes} min`
      : business.preparation_time_minutes
        ? `${business.preparation_time_minutes} min`
        : '15 min';

  // Use pre-calculated values from business service when available (search page)
  // Fall back to hook calculation for real-time updates (business detail page)
  const displayDeliveryTime = enableRealTimeUpdates
    ? (deliveryData.isLoading ? '...' : deliveryData.time)
    : (business.deliveryTime?.toString() || deliveryData.time || '...');

  const displayDeliveryTimeRange = enableRealTimeUpdates
    ? (deliveryData.isLoading ? 'Calculating...' : deliveryData.timeRange)
    : (business.deliveryTimeRange || deliveryData.timeRange || 'Calculating...');

  const displayDeliveryFee = enableRealTimeUpdates
    ? (deliveryData.isLoading ? 'Calculating...' : deliveryData.fee)
    : (business.deliveryFeeFormatted || deliveryData.fee || 'Calculating...');

  // Generate offers for demo purposes (same logic as mobile search)
  const generateOffers = () => {
    const offers = [];

    // Regular search results - use the existing logic
    if (index % 3 === 0) {
      offers.push({
        text: 'Get 25% off',
        color: 'bg-orange-500'
      });
    }

    if (index % 4 === 0) {
      offers.push({
        text: 'Spend £20',
        color: 'bg-purple-500'
      });
    }

    if (index % 7 === 0) {
      offers.push({
        text: 'Free delivery',
        color: 'bg-emerald-500'
      });
    }

    // If business has a deal property, add it as an offer
    if (business.deal || business.offer) {
      offers.push({
        text: business.deal || business.offer,
        color: business.deal?.includes('Free') ? 'bg-emerald-500' :
               business.deal?.includes('Buy One') ? 'bg-red-500' :
               business.deal?.includes('% Off') ? 'bg-orange-500' :
               'bg-red-500'
      });
    }

    return offers;
  };

  return (
    <div className={`animate-fadeIn animation-delay-${Math.min(index * 100, 600)} h-full`}>
      <PortraitBusinessCard
        id={business.id}
        name={business.name}
        image={imageUrl}
        businessType={businessType}
        rating={business.rating}
        deliveryTime={displayDeliveryTime}
        deliveryTimeRange={displayDeliveryTimeRange}
        deliveryFee={displayDeliveryFee}
        distance={business.distance}
        sponsored={index % 5 === 0} // Just for demo
        offers={generateOffers()}
        location={business.location}
        deliveryRadius={business.delivery_radius ? `${business.delivery_radius} miles` : undefined}
        preparationTime={preparationTime}
        deliveryAvailable={business.delivery_available !== false} // Default to true if not specified
        is_temporarily_closed={business.is_temporarily_closed}
        closure_message={business.closure_message}
        pickup_available={business.pickup_available}
        opening_hours={business.opening_hours}
      />
    </div>
  );
}
