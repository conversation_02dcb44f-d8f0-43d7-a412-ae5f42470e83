// Script to apply migration using direct database connection
const fs = require('fs');
const path = require('path');
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Error: Missing Supabase configuration');
  process.exit(1);
}

// Create Supabase client with service role key
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function createExecSqlFunction() {
  console.log('Creating exec_sql function...');
  
  const createFunctionSQL = `
    CREATE OR REPLACE FUNCTION exec_sql(sql TEXT)
    RETURNS TEXT
    LANGUAGE plpgsql
    SECURITY DEFINER
    AS $$
    BEGIN
      EXECUTE sql;
      RETURN 'Success';
    EXCEPTION
      WHEN OTHERS THEN
        RETURN 'Error: ' || SQLERRM;
    END;
    $$;
  `;
  
  try {
    // Try to create the function using a simple query
    const { data, error } = await supabase
      .from('information_schema.routines')
      .select('routine_name')
      .eq('routine_name', 'exec_sql')
      .limit(1);
    
    if (error) {
      console.log('Cannot check for existing function, attempting to create...');
    }
    
    // Try to execute the function creation directly
    const { data: result, error: execError } = await supabase.rpc('exec_sql', { sql: createFunctionSQL });
    
    if (execError) {
      console.log('Function creation failed, but continuing...');
      return false;
    }
    
    console.log('exec_sql function created successfully');
    return true;
  } catch (err) {
    console.log('Could not create exec_sql function:', err.message);
    return false;
  }
}

async function executeMigrationInChunks() {
  console.log('Reading migration file...');
  
  const migrationPath = path.join(__dirname, '..', 'supabase', 'migrations', '20240704000003_create_audit_trail_tables.sql');
  const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
  
  console.log(`Migration loaded (${migrationSQL.length} characters)`);
  
  // Split the SQL into individual statements
  const statements = migrationSQL
    .split(';')
    .map(stmt => stmt.trim())
    .filter(stmt => stmt.length > 0 && !stmt.startsWith('--') && !stmt.match(/^\s*$/));
  
  console.log(`Found ${statements.length} SQL statements`);
  
  let successCount = 0;
  let errorCount = 0;
  
  for (let i = 0; i < statements.length; i++) {
    const statement = statements[i] + ';'; // Add semicolon back
    console.log(`\nExecuting statement ${i + 1}/${statements.length}:`);
    console.log(statement.substring(0, 100) + (statement.length > 100 ? '...' : ''));
    
    try {
      const { data, error } = await supabase.rpc('exec_sql', { sql: statement });
      
      if (error) {
        console.error(`❌ Error in statement ${i + 1}:`, error.message);
        errorCount++;
        
        // Continue with next statement for non-critical errors
        if (error.message.includes('already exists') || error.message.includes('does not exist')) {
          console.log('   (Non-critical error, continuing...)');
        }
      } else {
        console.log(`✅ Statement ${i + 1} executed successfully`);
        successCount++;
      }
    } catch (err) {
      console.error(`❌ Exception in statement ${i + 1}:`, err.message);
      errorCount++;
    }
    
    // Small delay to avoid overwhelming the database
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  console.log(`\n📊 Migration Summary:`);
  console.log(`   ✅ Successful: ${successCount}`);
  console.log(`   ❌ Errors: ${errorCount}`);
  console.log(`   📝 Total: ${statements.length}`);
  
  return errorCount === 0;
}

async function verifyTables() {
  console.log('\n🔍 Verifying audit trail tables...');
  
  try {
    // Check if tables exist
    const { data: tables, error } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .in('table_name', ['order_time_estimates', 'order_delivery_updates']);
    
    if (error) {
      console.error('Error checking tables:', error);
      return false;
    }
    
    console.log('Found tables:', tables.map(t => t.table_name));
    
    if (tables.length === 2) {
      console.log('✅ Both audit trail tables exist!');
      return true;
    } else {
      console.log('⚠️  Some tables are missing');
      return false;
    }
  } catch (err) {
    console.error('Error verifying tables:', err);
    return false;
  }
}

async function main() {
  console.log('🚀 Starting audit trail migration...');
  
  // Try to create the exec_sql function first
  await createExecSqlFunction();
  
  // Execute the migration
  const success = await executeMigrationInChunks();
  
  // Verify the results
  const verified = await verifyTables();
  
  if (success && verified) {
    console.log('\n🎉 Migration completed successfully!');
  } else if (verified) {
    console.log('\n✅ Migration completed with some errors, but tables exist');
  } else {
    console.log('\n❌ Migration may have failed - please check manually');
  }
}

main().catch(err => {
  console.error('Unhandled error:', err);
  process.exit(1);
});
