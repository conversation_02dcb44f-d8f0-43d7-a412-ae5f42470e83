-- Migrate business closure data from businesses table to business_opening_hours table
-- This moves temporary closure functionality to the proper table structure

-- Step 1: Migrate existing closure data to opening hours table
-- For businesses that are temporarily closed, update all days of the week
INSERT INTO business_opening_hours (business_id, day_of_week, is_closed, closure_message, effective_date)
SELECT 
  b.id as business_id,
  generate_series(0, 6) as day_of_week, -- 0=Sunday through 6=Saturday
  b.is_temporarily_closed as is_closed,
  b.closure_message,
  CURRENT_DATE as effective_date
FROM businesses b
WHERE b.is_temporarily_closed = true
  AND b.closure_message IS NOT NULL
ON CONFLICT (business_id, day_of_week) 
DO UPDATE SET
  is_closed = EXCLUDED.is_closed,
  closure_message = EXCLUDED.closure_message,
  effective_date = EXCLUDED.effective_date,
  updated_at = NOW();

-- Step 2: Create default opening hours for businesses without any opening hours records
-- This ensures all businesses have opening hours entries for proper closure functionality
INSERT INTO business_opening_hours (business_id, day_of_week, open_time, close_time, is_closed, effective_date)
SELECT 
  b.id as business_id,
  generate_series(0, 6) as day_of_week,
  CASE 
    WHEN generate_series(0, 6) IN (1,2,3,4,5) THEN '11:00'::TIME -- Monday-Friday
    WHEN generate_series(0, 6) IN (0,6) THEN '12:00'::TIME -- Saturday-Sunday
  END as open_time,
  CASE 
    WHEN generate_series(0, 6) IN (1,2,3,4,5) THEN '22:00'::TIME -- Monday-Friday
    WHEN generate_series(0, 6) IN (0,6) THEN '23:00'::TIME -- Saturday-Sunday
  END as close_time,
  false as is_closed, -- Default to open
  CURRENT_DATE as effective_date
FROM businesses b
WHERE b.id NOT IN (
  SELECT DISTINCT business_id 
  FROM business_opening_hours
)
ON CONFLICT (business_id, day_of_week) DO NOTHING;

-- Step 3: Add unique constraint to prevent duplicate entries
-- This ensures data integrity for the business_id + day_of_week combination
ALTER TABLE business_opening_hours 
ADD CONSTRAINT IF NOT EXISTS unique_business_day 
UNIQUE (business_id, day_of_week);

-- Step 4: Remove the old closure columns from businesses table
-- These are no longer needed as closure data is now in business_opening_hours
ALTER TABLE businesses 
DROP COLUMN IF EXISTS is_temporarily_closed,
DROP COLUMN IF EXISTS closure_message;

-- Step 5: Drop the old index that was created for the removed column
DROP INDEX IF EXISTS idx_businesses_temporarily_closed;

-- Step 6: Add comment to document the change
COMMENT ON TABLE business_opening_hours IS 'Stores business opening hours and temporary closure information. Closure data moved from businesses table for better data organization.';
COMMENT ON COLUMN business_opening_hours.is_closed IS 'Override for temporary closure on this day. When true, business appears closed to customers.';
COMMENT ON COLUMN business_opening_hours.closure_message IS 'Message displayed to customers when business is temporarily closed on this day.';

-- Step 7: Verify the migration by checking sample data
-- This helps ensure the migration completed successfully
SELECT 
  b.id,
  b.name,
  COUNT(boh.id) as opening_hours_records,
  COUNT(CASE WHEN boh.is_closed = true THEN 1 END) as closed_days,
  STRING_AGG(DISTINCT boh.closure_message, '; ') as closure_messages
FROM businesses b
LEFT JOIN business_opening_hours boh ON b.id = boh.business_id
GROUP BY b.id, b.name
ORDER BY b.id
LIMIT 10;
