import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle
} from '@/components/ui/dialog';
import {
  Clock,
  MapPin,
  Phone,
  Mail,
  Star,
  AlertTriangle,
  Calendar
} from 'lucide-react';
import { Restaurant } from '@/types/business';
import { formatOpeningHours } from '@/lib/date-utils';

interface BusinessInfoDialogProps {
  business: Restaurant;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

interface OpeningHoursData {
  day_of_week: number;
  open_time: string | null;
  close_time: string | null;
  is_closed: boolean;
  closure_message: string | null;
}

const BusinessInfoDialog: React.FC<BusinessInfoDialogProps> = ({
  business,
  open,
  onOpenChange
}) => {
  const [openingHours, setOpeningHours] = useState<OpeningHoursData[]>([]);
  const [loading, setLoading] = useState(false);

  // Fetch opening hours from business_opening_hours table
  useEffect(() => {
    if (!open || !business.id) return;

    const fetchOpeningHours = async () => {
      setLoading(true);
      try {
        const response = await fetch(`/api/business-admin/business-data?businessId=${business.id}`);
        if (response.ok) {
          const data = await response.json();
          // Extract opening hours from the business data
          if (data.business_opening_hours) {
            setOpeningHours(data.business_opening_hours);
          }
        }
      } catch (error) {
        console.error('Error fetching opening hours:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchOpeningHours();
  }, [open, business.id]);

  // Format opening hours for display
  const formattedOpeningHours = openingHours.length > 0
    ? openingHours
        .sort((a, b) => a.day_of_week - b.day_of_week) // Sort by day of week
        .map((hours) => {
          const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
          const dayName = dayNames[hours.day_of_week];

          if (hours.is_closed || !hours.open_time || !hours.close_time) {
            return {
              day: dayName,
              hours: hours.closure_message || 'Closed'
            };
          }

          return {
            day: dayName,
            hours: `${hours.open_time} - ${hours.close_time}`
          };
        })
    : [];

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-xl">
            {business.name} Information
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6 py-4">
          {/* Description */}
          {business.description && (
            <div>
              <h3 className="font-semibold text-lg mb-2">About</h3>
              <p className="text-sm text-gray-700">{business.description}</p>
            </div>
          )}

          {/* Address & Contact */}
          <div>
            <h3 className="font-semibold text-lg mb-2">Contact Information</h3>
            <div className="space-y-3">
              {business.address && (
                <div className="flex items-start">
                  <MapPin className="h-5 w-5 text-gray-500 mr-2 mt-0.5 flex-shrink-0" />
                  <div>
                    <p className="text-sm font-medium">Address</p>
                    <p className="text-sm text-gray-600">{business.address}</p>
                    <p className="text-sm text-gray-600">{business.location}, Jersey</p>
                  </div>
                </div>
              )}
              
              {business.phone && (
                <div className="flex items-start">
                  <Phone className="h-5 w-5 text-gray-500 mr-2 mt-0.5 flex-shrink-0" />
                  <div>
                    <p className="text-sm font-medium">Phone</p>
                    <p className="text-sm text-gray-600">{business.phone}</p>
                  </div>
                </div>
              )}
              
              {business.email && (
                <div className="flex items-start">
                  <Mail className="h-5 w-5 text-gray-500 mr-2 mt-0.5 flex-shrink-0" />
                  <div>
                    <p className="text-sm font-medium">Email</p>
                    <p className="text-sm text-gray-600">{business.email}</p>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Opening Hours */}
          <div>
            <h3 className="font-semibold text-lg mb-2">Opening Hours</h3>
            {loading ? (
              <div className="text-sm text-gray-500">Loading opening hours...</div>
            ) : formattedOpeningHours.length > 0 ? (
              <div className="grid grid-cols-1 gap-2">
                {formattedOpeningHours.map((item) => (
                  <div key={item.day} className="flex justify-between">
                    <span className="text-sm font-medium">{item.day}</span>
                    <span className="text-sm text-gray-600">{item.hours}</span>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-sm text-gray-500">Opening hours not available</div>
            )}
          </div>

          {/* Hygiene Rating */}
          {business.hygiene_rating && (
            <div>
              <h3 className="font-semibold text-lg mb-2">Food Hygiene Rating</h3>
              <div className="flex items-center">
                <div className="bg-green-100 text-green-800 font-bold text-lg px-3 py-1 rounded-md mr-2">
                  {business.hygiene_rating}
                </div>
                <div>
                  <p className="text-sm text-gray-600">
                    Food hygiene rating
                  </p>
                  {business.last_inspection_date && (
                    <p className="text-xs text-gray-500 flex items-center mt-1">
                      <Calendar className="h-3 w-3 mr-1" />
                      Last inspected: {business.last_inspection_date}
                    </p>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Allergen Information */}
          <div>
            <h3 className="font-semibold text-lg mb-2">Allergen Information</h3>
            <div className="flex items-start">
              <AlertTriangle className="h-5 w-5 text-amber-500 mr-2 mt-0.5 flex-shrink-0" />
              <p className="text-sm text-gray-600">
                {Array.isArray(business.allergen_info) && business.allergen_info.length > 0
                  ? business.allergen_info.join(', ')
                  : typeof business.allergen_info === 'string' && business.allergen_info
                  ? business.allergen_info
                  : "Please contact the restaurant directly for detailed allergen information. All dishes may contain traces of allergens."}
              </p>
            </div>
          </div>


        </div>
      </DialogContent>
    </Dialog>
  );
};

export default BusinessInfoDialog;
