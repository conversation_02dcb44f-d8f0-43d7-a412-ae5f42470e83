// Script to apply the audit trail migration using Supabase Management API
const fs = require('fs');
const path = require('path');
require('dotenv').config({ path: '.env.local' });

// Get project reference from the URL
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const projectRef = supabaseUrl.split('//')[1].split('.')[0]; // Extract bhbxhzisfzclwuyclfqn
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

console.log('Project Reference:', projectRef);
console.log('Supabase URL:', supabaseUrl);

if (!projectRef || !supabaseServiceKey) {
  console.error('Error: Missing Supabase configuration');
  process.exit(1);
}

async function applyAuditTrailMigration() {
  try {
    console.log('Reading audit trail migration file...');
    
    // Read the migration file
    const migrationPath = path.join(__dirname, '..', 'supabase', 'migrations', '20240704000003_create_audit_trail_tables.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    console.log(`Migration SQL loaded (${migrationSQL.length} characters)`);
    console.log('Applying migration via Supabase Management API...');
    
    // Use the Supabase Management API to execute the SQL
    const response = await fetch(`https://api.supabase.com/v1/projects/${projectRef}/database/query`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${supabaseServiceKey}`
      },
      body: JSON.stringify({
        query: migrationSQL
      })
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error('API Error Response:', response.status, response.statusText);
      console.error('Error Details:', errorText);
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const result = await response.json();
    console.log('Migration applied successfully!');
    console.log('Result:', result);
    
    // Verify the tables were created
    console.log('\nVerifying audit trail tables were created...');
    
    const verifyResponse = await fetch(`https://api.supabase.com/v1/projects/${projectRef}/database/query`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${supabaseServiceKey}`
      },
      body: JSON.stringify({
        query: `
          SELECT table_name 
          FROM information_schema.tables 
          WHERE table_schema = 'public' 
          AND table_name IN ('order_time_estimates', 'order_delivery_updates')
          ORDER BY table_name;
        `
      })
    });
    
    if (verifyResponse.ok) {
      const verifyResult = await verifyResponse.json();
      console.log('Verification result:', verifyResult);
      
      if (verifyResult.length === 2) {
        console.log('✅ Both audit trail tables created successfully!');
      } else {
        console.log('⚠️  Some tables may not have been created properly');
      }
    } else {
      console.log('Could not verify table creation, but migration completed');
    }
    
  } catch (error) {
    console.error('Error applying migration:', error);
    process.exit(1);
  }
}

// Run the migration
applyAuditTrailMigration();
