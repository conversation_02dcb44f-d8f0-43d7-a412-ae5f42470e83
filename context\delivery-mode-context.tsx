"use client"

import { createContext, useContext, useState, useEffect, type ReactNode } from "react"

export type DeliveryMode = "delivery" | "pickup"

interface DeliveryModeContextType {
  mode: DeliveryMode
  setMode: (mode: DeliveryMode) => void
  isHydrated: boolean
}

// Create a context with default values
const DeliveryModeContext = createContext<DeliveryModeContextType>({
  mode: "delivery",
  setMode: () => {},
  isHydrated: false
})

// Enhanced provider that manages customer's global delivery preference
export function DeliveryModeProvider({ children }: { children: ReactNode }) {
  const [mode, setModeState] = useState<DeliveryMode>("delivery")
  const [isHydrated, setIsHydrated] = useState(false)

  // Load saved preference from localStorage on mount
  useEffect(() => {
    const savedMode = localStorage.getItem('loop_jersey_delivery_mode') as DeliveryMode
    if (savedMode === "pickup" || savedMode === "delivery") {
      setModeState(savedMode)
    }
    setIsHydrated(true)
  }, [])

  // Save preference to localStorage when it changes
  const setMode = (newMode: DeliveryMode) => {
    setModeState(newMode)
    localStorage.setItem('loop_jersey_delivery_mode', newMode)
  }

  return (
    <DeliveryModeContext.Provider
      value={{
        mode,
        setMode,
        isHydrated
      }}
    >
      {children}
    </DeliveryModeContext.Provider>
  )
}

// Hook to use the delivery mode context
export function useDeliveryMode() {
  return useContext(DeliveryModeContext)
}
