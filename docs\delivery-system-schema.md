# Delivery System Database Schema

## Overview
This document outlines the new database schema for the enhanced delivery system, including opening hours, parish restrictions, and flexible delivery pricing.

## 1. Business Opening Hours

```sql
-- Replace JSON opening hours with relational structure
CREATE TABLE business_opening_hours (
  id SERIAL PRIMARY KEY,
  business_id INTEGER NOT NULL REFERENCES businesses(id) ON DELETE CASCADE,
  
  -- Day of week (0=Sunday, 1=Monday, ..., 6=Saturday)
  day_of_week INTEGER NOT NULL CHECK (day_of_week >= 0 AND day_of_week <= 6),
  
  -- Opening times (NULL = closed that day)
  open_time TIME,
  close_time TIME,
  
  -- Temporary closure overrides
  is_closed BOOLEAN DEFAULT false, -- Override for temporary closure
  closure_message TEXT, -- "Back in the New Year", "Closed for maintenance"
  
  -- Temporary schedule changes
  effective_date DATE, -- When this schedule starts (for temporary changes)
  expires_date DATE, -- When to revert to normal hours (NULL = permanent)
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Ensure one record per business per day (for current schedule)
  UNIQUE(business_id, day_of_week, effective_date)
);

-- Index for efficient lookups
CREATE INDEX idx_business_opening_hours_business_day ON business_opening_hours(business_id, day_of_week);
CREATE INDEX idx_business_opening_hours_effective ON business_opening_hours(effective_date, expires_date);
```

## 2. Core Delivery Configuration

```sql
-- Main delivery configuration per business
CREATE TABLE business_delivery_config (
  id SERIAL PRIMARY KEY,
  business_id INTEGER NOT NULL REFERENCES businesses(id) ON DELETE CASCADE,
  
  -- Service availability
  pickup_enabled BOOLEAN DEFAULT true,
  delivery_enabled BOOLEAN DEFAULT false,
  use_loop_delivery BOOLEAN DEFAULT true, -- Only relevant if delivery_enabled
  
  -- Geographic restrictions logic
  restriction_logic VARCHAR(10) DEFAULT 'either' CHECK (restriction_logic IN ('either', 'both')),
  -- 'either' = exclude if EITHER parish OR distance exceeds
  -- 'both' = exclude if BOTH parish AND distance exceed
  
  -- Distance-based restrictions
  delivery_radius NUMERIC(5,2), -- NULL = no distance limit
  
  -- Timing options
  pickup_asap_available BOOLEAN DEFAULT true,
  pickup_scheduled_available BOOLEAN DEFAULT false,
  delivery_asap_available BOOLEAN DEFAULT false,
  delivery_scheduled_available BOOLEAN DEFAULT false,
  
  -- Advance booking settings
  min_advance_booking_minutes INTEGER DEFAULT 30,
  max_advance_booking_days INTEGER DEFAULT 7,
  
  -- Order value limits
  minimum_order_value NUMERIC(10,2) DEFAULT 15.00,
  maximum_order_value NUMERIC(10,2) DEFAULT 200.00, -- Fraud protection
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(business_id)
);
```

## 3. Parish Master Data

```sql
-- Jersey parishes reference data
CREATE TABLE parishes (
  id SERIAL PRIMARY KEY,
  name VARCHAR(50) NOT NULL UNIQUE,
  postcode_prefixes TEXT[] NOT NULL, -- ['JE1', 'JE2'] for St. Helier
  is_remote BOOLEAN DEFAULT false, -- Flag for remote parishes
  average_delivery_time_minutes INTEGER DEFAULT 30,
  coordinates POINT, -- Parish center for distance calculations
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert Jersey parishes
INSERT INTO parishes (name, postcode_prefixes, is_remote, average_delivery_time_minutes) VALUES
('St. Helier', ARRAY['JE1', 'JE2'], false, 20),
('St. Saviour', ARRAY['JE2', 'JE7'], false, 25),
('St. Clement', ARRAY['JE2'], false, 25),
('St. Brelade', ARRAY['JE3'], false, 30),
('St. Peter', ARRAY['JE3'], false, 35),
('St. Lawrence', ARRAY['JE3'], false, 35),
('St. Mary', ARRAY['JE3'], true, 40),
('St. Ouen', ARRAY['JE3'], true, 45),
('St. John', ARRAY['JE3'], true, 40),
('Trinity', ARRAY['JE3'], true, 40),
('St. Martin', ARRAY['JE3'], false, 35),
('Grouville', ARRAY['JE3'], false, 30);
```

## 4. Parish Delivery Restrictions

```sql
-- Business parish delivery preferences (whitelist approach)
CREATE TABLE business_parish_delivery (
  id SERIAL PRIMARY KEY,
  business_id INTEGER NOT NULL REFERENCES businesses(id) ON DELETE CASCADE,
  parish_id INTEGER NOT NULL REFERENCES parishes(id) ON DELETE CASCADE,
  
  -- Parish-specific overrides
  delivery_fee_override NUMERIC(10,2), -- Override default delivery fee for this parish
  minimum_order_override NUMERIC(10,2), -- Override minimum order for this parish
  estimated_delivery_minutes INTEGER, -- Override default delivery time
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  PRIMARY KEY (business_id, parish_id)
);

-- Index for efficient parish lookups
CREATE INDEX idx_business_parish_delivery_business ON business_parish_delivery(business_id);
CREATE INDEX idx_business_parish_delivery_parish ON business_parish_delivery(parish_id);
```

## 5. Delivery Pricing Configuration

```sql
-- Flexible delivery pricing per business
CREATE TABLE business_delivery_pricing (
  id SERIAL PRIMARY KEY,
  business_id INTEGER NOT NULL REFERENCES businesses(id) ON DELETE CASCADE,
  
  -- Pricing model (businesses choose one primary model)
  pricing_model VARCHAR(20) NOT NULL DEFAULT 'fixed' 
    CHECK (pricing_model IN ('fixed', 'distance', 'parish', 'fixed_plus_distance')),
  
  -- Fixed pricing
  base_delivery_fee NUMERIC(10,2) DEFAULT 2.50,
  
  -- Distance-based pricing
  fee_per_km NUMERIC(10,2) DEFAULT 0.50,
  
  -- Value-based discounts
  free_delivery_threshold NUMERIC(10,2), -- Free delivery over £X
  
  -- Time-based multipliers
  asap_fee_multiplier NUMERIC(3,2) DEFAULT 1.0, -- 1.0 = normal, 1.5 = 50% extra
  scheduled_fee_multiplier NUMERIC(3,2) DEFAULT 1.0,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(business_id)
);
```

## 6. Migration Strategy

```sql
-- Migration to populate opening hours from existing JSON data
-- This will be handled in a separate migration script that:
-- 1. Reads existing opening_hours JSON from businesses table
-- 2. Converts to relational format in business_opening_hours
-- 3. Migrates existing delivery settings to new tables
-- 4. Removes old columns after verification
```

## 7. Indexes for Performance

```sql
-- Additional indexes for common queries
CREATE INDEX idx_business_delivery_config_enabled ON business_delivery_config(delivery_enabled, pickup_enabled);
CREATE INDEX idx_business_opening_hours_lookup ON business_opening_hours(business_id, day_of_week, effective_date, expires_date);
CREATE INDEX idx_parishes_postcode_lookup ON parishes USING GIN(postcode_prefixes);
```

## Notes

1. **Temporary Closures**: The `is_closed` flag and `closure_message` in opening hours always override normal hours
2. **Parish Restrictions**: Whitelist approach - only parishes in `business_parish_delivery` are allowed
3. **Pricing Conflicts**: Application logic will prevent conflicting pricing models (e.g., distance + parish)
4. **Restriction Logic**: Businesses can choose 'either' or 'both' logic for parish/distance restrictions
5. **Order Limits**: Max order value applies to both pickup and delivery for fraud protection
